/* color palette from <https://github.com/vuejs/theme> */

:root {
  --max-container: 750px;
  --body-bg-color: #08172b;
  --block-bg-color: #263357;
  --border-color: #294273;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  overflow: hidden;
  overscroll-behavior: none;
  background-color: var(--body-bg-color);
  min-height: 100%;
  line-height: 1;
  font-size: var(--text-default);
  font-family: Inter, -apple-system, Roboto, 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100%;
  height: 100%;
}

@keyframes modal-mask-show {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-mask-hide {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes modal-inner-show {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modal-inner-hide {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}