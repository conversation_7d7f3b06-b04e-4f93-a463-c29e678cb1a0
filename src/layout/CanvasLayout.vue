<script setup lang="ts">
import {useCanvasLayout} from "@/composables/useCanvasLayout.ts";

/** 设计稿375px，标准尺寸10px */
useCanvasLayout(375, 10);
</script>

<template>
  <div class="view-wrapper">
    <div class="view-content">
      <slot />
    </div>
  </div>
</template>

<style>
.view-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-content {
  user-select: none;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
