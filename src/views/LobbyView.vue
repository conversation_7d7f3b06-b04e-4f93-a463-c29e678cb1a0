<script setup lang="ts">
import {useDisableZoom} from "@/composables/useDisableZoom.ts";

import LayoutHeader from "@/components/LayoutHeader/index.vue";
import LayoutBody from "@/components/LayoutBody/index.vue";
import PixiEngine from "./PixiEngine/index.vue";

useDisableZoom();
</script>

<template>
  <div id="view-box">
    <PixiEngine />
  </div>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>