import { Container } from "pixi.js";
import { Chicken, ChickenState } from "./Chicken";
import { Point } from "pixi.js";

/**
 * 使用 Chicken 类的示例
 */
export class ChickenExample extends Container {
  private chicken: Chicken;

  constructor() {
    super();
    
    // 创建小鸡实例
    this.chicken = new Chicken({
      startPosition: new Point(100, 500),
      sceneHeight: 1080,
      jumpSpeed: 15,
      jumpDuration: 800
    });

    // 添加到容器
    this.addChild(this.chicken.displayObject);

    // 设置事件监听
    this.setupEventListeners();
    
    // 设置键盘控制
    this.setupKeyboardControls();
  }

  private setupEventListeners(): void {
    // 监听状态变化
    this.chicken.onStateChanged((state) => {
      console.log(`小鸡状态变为: ${state}`);
      
      switch (state) {
        case ChickenState.IDLE:
          console.log("小鸡进入待机状态");
          break;
        case ChickenState.JUMP:
          console.log("小鸡开始跳跃");
          break;
        case ChickenState.DEAD:
          console.log("小鸡死亡");
          break;
      }
    });

    // 监听跳跃开始
    this.chicken.onJumpStarted(() => {
      console.log("跳跃动作开始 - 可以在这里添加音效");
    });

    // 监听跳跃结束
    this.chicken.onJumpEnded(() => {
      console.log("跳跃动作结束 - 可以在这里检查碰撞");
    });
  }

  private setupKeyboardControls(): void {
    // 添加键盘事件监听
    document.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'Space':
          event.preventDefault();
          this.chicken.jump();
          break;
        case 'KeyR':
          event.preventDefault();
          this.chicken.reset();
          break;
        case 'KeyD':
          event.preventDefault();
          this.chicken.die();
          break;
        case 'KeyI':
          event.preventDefault();
          this.chicken.idle();
          break;
      }
    });
  }

  public update(): void {
    // 更新小鸡
    this.chicken.update();
  }

  public resize(screenHeight: number): void {
    // 调整小鸡大小
    this.chicken.resize(screenHeight);
  }

  public destroy(): void {
    // 清理资源
    this.chicken.destroy();
    super.destroy();
  }

  // 获取小鸡信息的方法
  public getChickenInfo(): string {
    return `
      位置: (${this.chicken.position.x}, ${this.chicken.position.y})
      状态: ${this.chicken.state}
      是否跳跃中: ${this.chicken.jumping}
      尺寸: ${this.chicken.width} x ${this.chicken.height}
    `;
  }
}
