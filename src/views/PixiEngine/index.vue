<script setup lang="ts">
import { onMounted, onBeforeUnmount, useTemplateRef } from 'vue';
import { CreationEngine, setEngine, destroyEngine } from './engine';
import { MainScreen } from './MainScreen';
import { Assets } from 'pixi.js';

const root = useTemplateRef('rootRef');

let app : CreationEngine | null = null;

onMounted(async () => {
  if (!root.value) throw new Error('Root element not found');

  app = new CreationEngine();

  if (!app) throw new Error('Failed to create app');

  setEngine(app);

  await app.init({
    root: root.value,
    resizeTo: root.value,
    autoDensity: true,
    backgroundColor: 0x716c69,
  });

  await Assets.loadBundle(MainScreen.assetBundles);

  const MainScreenContainer = new MainScreen();

  app.stage.addChild(MainScreenContainer);

  app.renderer.on('resize', () => {
    MainScreenContainer.resize();
  });

  app.ticker.add(MainScreenContainer.update.bind(MainScreenContainer));
});

onBeforeUnmount(() => {
  destroyEngine();
});
</script>

<template>
  <div ref="rootRef" class="w-full h-full"></div>
</template>

<style lang="scss" scoped>

</style>