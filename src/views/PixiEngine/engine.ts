import type { ApplicationOptions, DestroyOptions, RendererDestroyOptions } from "pixi.js";
import { Application, Assets } from "pixi.js";
import { sound } from "@pixi/sound";
import "pixi.js/app";

import manifest from "@/manifest.json";

let instance: CreationEngine | null = null;

/**
 * Get the main application engine
 * This is a simple way to access the engine instance from anywhere in the app
 */
export function getEngine(): CreationEngine {
  return instance!;
}

export function setEngine(app: CreationEngine) {
  instance = app;
}

export function destroyEngine() {
  instance?.destroy();
  instance = null;
}

export function getResolution(): number {
  let resolution = Math.max(window.devicePixelRatio, 2);

  if (resolution % 1 !== 0) {
    resolution = 2;
  }

  return resolution;
}


/**
 * The main creation engine class.
 *
 * This is a lightweight wrapper around the PixiJS Application class.
 * It provides a few additional features such as:
 * - Navigation manager
 * - Audio manager
 * - Resize handling
 * - Visibility change handling (pause/resume sounds)
 *
 * It also initializes the PixiJS application and loads any assets in the `preload` bundle.
 */
export class CreationEngine extends Application {
  /** Initialize the application */
  public async init(opts: Partial<ApplicationOptions> & { root: string | HTMLElement }): Promise<void> {
    opts.resizeTo ??= window;
    opts.resolution ??= getResolution();

    // 添加渲染器配置以更好地处理 Alpha 混合
    // opts.backgroundAlpha ??= 1;
    // opts.antialias ??= true;
    // opts.premultipliedAlpha ??= false; // 禁用预乘 Alpha

    await super.init(opts);

    const root = typeof opts.root === 'string' ? document.querySelector(opts.root) : opts.root;
  
    if (!root) {
      throw new Error(`Root element not found: ${opts.root}`);
    }

    // Append the application canvas to the document body
    root.appendChild(this.canvas);

    // Add a visibility listener, so the app can pause sounds and screens
    document.addEventListener("visibilitychange", this.visibilityChange);

    // Init PixiJS assets with this asset manifest
    await Assets.init({ manifest, basePath: "assets" });
    await Assets.loadBundle("preload");

    // List all existing bundles names
    const allBundles = manifest.bundles.map((item) => item.name);
    // Start up background loading of all bundles
    Assets.backgroundLoadBundle(allBundles);
  }

  public override destroy(
    rendererDestroyOptions: RendererDestroyOptions = false,
    options: DestroyOptions = false,
  ): void {
    document.removeEventListener("visibilitychange", this.visibilityChange);
    super.destroy(rendererDestroyOptions, options);
  }

  /** Fire when document visibility changes - lose or regain focus */
  protected visibilityChange = () => {
    if (document.hidden) {
      sound.pauseAll();
    } else {
      sound.resumeAll();
    }
  };
}
