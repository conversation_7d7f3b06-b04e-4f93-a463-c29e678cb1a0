import { Point } from 'pixi.js';
import { Spine } from '@esotericsoftware/spine-pixi-v8';

export enum ChickenState {
  IDLE = 'idle',
  JUMP = 'jump',
  DEAD = 'death',
  WIN = 'win',
}

export interface ChickenConfig {
  sceneHeight?: number;
  startPosition?: Point;
  step?: number;
}

export class Chicken {
  private spine: Spine;
  private sceneHeight: number;
  private startPosition: Point;
  private currentX: number;
  private step: number;
  private jumpSpeed: number;
  private jumpStartDuration = 300;
  private jumpEndDuration = 500;
  private isJumping = false;

  private currentState: ChickenState = ChickenState.IDLE;
  
  // 事件回调
  private onStateChange?: (state: ChickenState) => void;
  private onJumpStart?: () => void;
  private onJumpEnd?: () => void;

  constructor(config: ChickenConfig = {}) {
    // 配置默认值
    this.sceneHeight = config.sceneHeight || 1080;
    this.startPosition = config.startPosition || new Point(270, 900);
    this.currentX = this.startPosition.x;
    this.step = config.step || 0;
    this.jumpSpeed = 15;
    
    // 创建 Spine 动画
    this.spine = Spine.from({ 
      skeleton: "chiken.json", 
      atlas: "chiken.atlas" 
    });
    
    // 设置初始状态
    this.setState(ChickenState.IDLE);
    this.setupAnimationListeners();
  }

  private setupAnimationListeners(): void {
    this.spine.state.addListener({
      start: (entry) => {
        if (entry.animation?.name === ChickenState.JUMP) {
          this.handleJumpStart();
        }
      },
      
      complete: (entry) => {
        if (entry.animation?.name === ChickenState.JUMP) {
          this.handleJumpEnd();
        }
      },
      
      end: (entry) => {
        console.log('Animation ended:', entry.animation?.name);
      }
    });
  }

  private handleJumpStart(): void {
    setTimeout(() => {
      this.isJumping = true;
      this.onJumpStart?.();
    }, this.jumpStartDuration);

    setTimeout(() => {
      this.isJumping = false;
      this.onJumpEnd?.();
    }, this.jumpEndDuration);
  }

  private handleJumpEnd(): void {
    this.setState(ChickenState.IDLE);
  }

  private setState(state: ChickenState): void {
    this.currentState = state;
    this.spine.state.setAnimation(0, state, state === ChickenState.IDLE);
    // this.spine.state.clearTracks();
    // 回到待机状态
    // this.spine.state.addAnimation(0, ChickenState.IDLE, true, 0);
    this.onStateChange?.(state);
  }

  // 公共方法
  public jump(x: numb): void {
    if (this.isJumping || this.currentState === ChickenState.JUMP) return;
    this.setState(ChickenState.JUMP);
    this.step += 1;
  }

  public idle(): void {
    if (this.currentState === ChickenState.IDLE) return;
    this.setState(ChickenState.IDLE);
  }

  public die(): void {
    if (this.currentState === ChickenState.DEAD) return;
    this.setState(ChickenState.DEAD);
  }

  public update(): void {
    if (this.isJumping) {
      // 跳跃时向前移动
      this.currentX += this.jumpSpeed;
      this.spine.position.x = this.currentX;
    }
  }

  public resize(screenHeight: number): void {
    const scale = screenHeight / this.sceneHeight;
    this.spine.scale.set(scale);
    this.spine.position.set(
      scale * this.currentX, 
      scale * this.startPosition.y
    );
  }

  public reset(): void {
    this.currentX = this.startPosition.x;
    this.isJumping = false;
    this.setState(ChickenState.IDLE);
  }

  // Getters
  public get displayObject(): Spine {
    return this.spine;
  }

  public get position(): Point {
    return new Point(this.currentX, this.startPosition.y);
  }

  public get jumping(): boolean {
    return this.isJumping;
  }

  public get state(): ChickenState {
    return this.currentState;
  }

  public get width(): number {
    return this.spine.width;
  }

  public get height(): number {
    return this.spine.height;
  }

  // 事件监听器设置
  public onStateChanged(callback: (state: ChickenState) => void): void {
    this.onStateChange = callback;
  }

  public onJumpStarted(callback: () => void): void {
    this.onJumpStart = callback;
  }

  public onJumpEnded(callback: () => void): void {
    this.onJumpEnd = callback;
  }

  // 销毁方法
  public destroy(): void {
    this.spine.destroy();
  }
}
