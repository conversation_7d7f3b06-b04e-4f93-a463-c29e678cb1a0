import { Point, type Ticker } from "pixi.js";
import { Container, Graphics, Sprite, Texture, TilingSprite } from "pixi.js";
import { Spine } from '@esotericsoftware/spine-pixi-v8';
import { interpolate } from "gsap";
import { getEngine } from "./engine";

interface SpriteInfo {
  name: string;
  width: number;
  height: number;
  ratio: number;
  sprite: Sprite | TilingSprite;
}

interface ChikenInfo {
  width: number;
  height: number;
  ratio: number;
  spine: Spine;
}

export class MainScreen extends Container {
  /** Assets bundles required by this screen */
  public static assetBundles = ["main", "spine"];

  public container: Container;

  private roadStart: SpriteInfo;
  private roadEnd: SpriteInfo;
  private lanes: SpriteInfo[] = [];
  private laneLines: SpriteInfo[] = [];
  private laneCount = 30;


  private chikenSpine: ChikenInfo;
  private chikenStartPoint: Point = new Point(270, 900);
  private chikenX = 270;
  private chikenJumping = false;
  
  private sceneHeight = 1080;
  private laneLineCount = 10; // 车道线 线段 数量
  private laneLineScale = 11 / 10; // 车道线放大倍数，让线段更长一些
  
  private paused = false;
  private dragging = false;
  private daragX = 0;

  constructor() { 
    super(); 

    this.container = new Container();

    this.addChild(this.container);

    // 创建马路开始部分
    const roadStart = Texture.from('road-start.png');
    const roadStartSprite = new Sprite(roadStart);
    this.roadStart = {
      name: 'road-start',
      width: roadStart.width,
      height: roadStart.height,
      ratio: roadStart.width / roadStart.height,
      sprite: roadStartSprite,
    };
    this.container.addChild(roadStartSprite);


    // 创建车道
    for (let i = 0; i < this.laneCount; i++) {
      const lane = this.createLane();
      this.container.addChild(lane);
      this.lanes.push({
        name: `road-${i}`,
        width: lane.width,
        height: lane.height,
        ratio: lane.width / lane.height,
        sprite: lane,
      });

      if (i < this.laneCount - 1) {
        const laneLine = this.createLaneLine();
        this.container.addChild(laneLine);
        this.laneLines.push({
          name: `road-line-${i}`,
          width: laneLine.width,
          height: laneLine.height,
          ratio: laneLine.width / laneLine.height,
          sprite: laneLine,
        });
      }
    }

    // 创建马路结束部分
    const roadEnd = Texture.from('road-end.png');
    const roadEndSprite = new Sprite(roadEnd);
    const roadEndRatio = roadEnd.width / roadEnd.height;
    this.roadEnd = {
      name: 'road-end',
      width: roadEnd.width,
      height: roadEnd.height,
      ratio: roadEndRatio,
      sprite: roadEndSprite,
    };
    this.container.addChild(roadEndSprite);

    // 创建小鸡
    const chiken = Spine.from({ skeleton: "chiken.json", atlas: "chiken.atlas" });
    this.chikenSpine = {
      ratio: chiken.height / this.sceneHeight,
      width: chiken.width,
      height: chiken.height,
      spine: chiken,
    };
    this.chikenSpine.spine.state.setAnimation(0, 'idle', true);
    this.chikenSpine.spine.state.addListener({
      start: (entry) => {
        if (entry.animation?.name === 'jump') {
          setTimeout(() => this.chikenJumping = true, 300);
          setTimeout(() => this.chikenJumping = false, 600);
          console.log('start', entry, entry.animation?.name);
        }
      },
      end: (entry) => {
        console.log('end', entry, entry.animation?.name);
        if (entry.animation?.name === 'jump') {
        }
      },
      complete: (entry) => {
        console.log('complete', entry, entry.animation?.name);
        if (entry.animation?.name === 'jump') {
        }
      },
    });
    this.container.addChild(chiken);
    this.chikenSpine.spine.interactive = true;
    this.chikenSpine.spine.addEventListener('pointertap', () => {
      this.chikenSpine.spine.state.setAnimation(0, 'jump', false);
      this.chikenSpine.spine.state.addAnimation(0, 'idle', true, 0);
    })

    this.initDragMap();
    this.resize();
  }

  // 车道
  private createLane() {
    const engine = getEngine();
    const laneWidth = 250;
    const laneContainer = new Container();

    // 马路
    const lane = new Graphics();
    lane.rect(0, 0, laneWidth, this.sceneHeight).fill(0x716c69);
    laneContainer.addChild(lane);

    // 井盖
    const hatchScale = 0.7;
    const hatch = Texture.from('hatch_1.png');
    const hatchSprite = new Sprite(hatch);
    hatchSprite.x = laneWidth * 0.5;
    hatchSprite.y = this.sceneHeight * 0.75;
    hatchSprite.anchor.set(0.5);
    hatchSprite.width = laneWidth * hatchScale;
    hatchSprite.height = laneWidth * hatchScale;
    laneContainer.addChild(hatchSprite);

    // 使用 renderer.generateTexture 生成纹理
    const laneTexture = engine.renderer.generateTexture(laneContainer);

    return new Sprite(laneTexture);
  }

  // 车道线
  private createLaneLine() {
    const engine = getEngine();
    const laneLineWidth = 8;
    const laneLineHeight = this.sceneHeight / this.laneLineCount;
    
    const laneLine = new Graphics();

    laneLine.rect(0, 0, laneLineWidth, laneLineHeight).fill(0x716c69);
    laneLine.rect(0, laneLineHeight * 0.25, laneLineWidth, laneLineHeight * 0.5).fill(0xd3cfc9);

    const laneLineTexture = engine.renderer.generateTexture(laneLine);
    const laneLineSprite = new TilingSprite(laneLineTexture);

    laneLineSprite.height = this.sceneHeight;
    // 设置锚点往上偏移
    laneLineSprite.anchor.set(0, (this.laneLineScale - 1) * 0.5);

    return laneLineSprite;
  }

  // 初始化地图拖拽
  private initDragMap() {
    const engine = getEngine();

    this.container.interactive = true;

    this.container.on('pointerdown', e => {
      this.dragging = true;
      this.daragX = e.x;
    });

    this.container.addEventListener('pointermove', (e) => {
      if (!this.dragging) return;
      const offset = e.x - this.daragX;
      const move = Math.max(Math.min(0, this.container.x += offset), engine.screen.width - this.container.width);
      this.container.x = move;
      this.daragX = e.x;
    });

    this.container.addEventListener('pointerup', (e) => {
      this.dragging = false;
    });

    this.container.addEventListener('pointerleave', (e) => {
      this.dragging = false;
    });

    this.container.addEventListener('pointercancel', (e) => {
      this.dragging = false;
    });
  }

  /** Update the screen */
  public update(_time: Ticker) {
    if (!this.chikenJumping) return;
    const engine = getEngine();
    const { height } = engine.renderer;
    const chikenScale = height / this.sceneHeight;

    this.chikenX += 10;
    this.chikenSpine.spine.scale.set(chikenScale);
    this.chikenSpine.spine.position.set(chikenScale * this.chikenX, chikenScale * this.chikenStartPoint.y);
  }

  /** Resize the screen, fired whenever window size changes */
  public resize() {
    const engine = getEngine();

    const { height } = engine.renderer;

    let x = 0;

    // 调整马路开始部分
    const roadStartScale = height / this.roadStart.height;
    this.roadStart.sprite.x = x;
    this.roadStart.sprite.scale.set(roadStartScale);
    x += this.roadStart.sprite.width;

    for(let i = 0; i < this.laneCount; i++) {
      // 调整车道
      const lane = this.lanes[i];
      const laneScale = height / lane.height;
      lane.sprite.x = x;
      lane.sprite.scale.set(laneScale);
      x += lane.sprite.width;

      // 调整车道线
      if (i < this.laneCount - 1) {
        const laneLine = this.laneLines[i];
        const laneLineScale = height / laneLine.height * this.laneLineScale;
        laneLine.sprite.x = x;
        laneLine.sprite.scale.set(laneLineScale);
        x += laneLine.sprite.width;
      }
    }

    // 调整马路结束部分
    const roadEndScale = height / this.roadEnd.height;
    this.roadEnd.sprite.x = x;
    this.roadEnd.sprite.scale.set(roadEndScale);
    x += this.roadEnd.sprite.width;

    // 调整小鸡
    const chikenScale = height / this.sceneHeight;
    this.chikenSpine.spine.scale.set(chikenScale);
    this.chikenSpine.spine.position.set(chikenScale * this.chikenX, chikenScale * this.chikenStartPoint.y);
    // this.chikenSpine.spine.x = chikenScale * 270;
    // this.chikenSpine.spine.y = chikenScale * 900;
  }

  /** Pause gameplay - automatically fired when a popup is presented */
  public async pause() {
    this.container.interactiveChildren = false;
    this.paused = true;
  }

  /** Resume gameplay */
  public async resume() {
    this.container.interactiveChildren = true;
    this.paused = false;
  }

  /** Fully reset */
  public reset() {}

  /** Show screen with animations */
  public async show(): Promise<void> {}

  /** Hide screen with animations */
  public async hide() {}

  /** Auto pause the app when window go out of focus */
  public blur() {}
}
