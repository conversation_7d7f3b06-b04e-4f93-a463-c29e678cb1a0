import type { FederatedPointerEvent, Ticker } from "pixi.js";
import { Assets, Container, Graphics, Sprite, Texture, TilingSprite } from "pixi.js";
import { getEngine } from "./engine";

interface SpriteInfo {
  name: string;
  width: number;
  height: number;
  ratio: number;
  sprite: Sprite | TilingSprite;
}

/** The screen that holds the app */
export class MainScreen extends Container {
  /** Assets bundles required by this screen */
  public static assetBundles = ["main"];

  public container: Container;

  private roadStart: SpriteInfo;
  private roadEnd: SpriteInfo;
  private lanes: SpriteInfo[] = [];
  private laneLines: SpriteInfo[] = [];
  private laneCount = 30;
  
  private paused = false;
  private dragging = false;
  private daragX = 0;

  constructor() { 
    super(); 

    this.container = new Container();

    this.addChild(this.container);

    // 创建马路开始部分
    const roadStart = Texture.from('road-start.png');
    const roadStartSprite = new Sprite(roadStart);
    this.roadStart = {
      name: 'road-start',
      width: roadStart.width,
      height: roadStart.height,
      ratio: roadStart.width / roadStart.height,
      sprite: roadStartSprite,
    };
    this.container.addChild(roadStartSprite);


    // 创建车道
    for (let i = 0; i < this.laneCount; i++) {
      const lane = this.createLane();
      this.container.addChild(lane);
      this.lanes.push({
        name: `road-${i}`,
        width: lane.width,
        height: lane.height,
        ratio: lane.width / lane.height,
        sprite: lane,
      });

      if (i < this.laneCount - 1) {
        const laneLine = this.createLaneLine();
        this.container.addChild(laneLine);
        this.laneLines.push({
          name: `road-line-${i}`,
          width: laneLine.width,
          height: laneLine.height,
          ratio: laneLine.width / laneLine.height,
          sprite: laneLine,
        });
      }
    }

    // 创建马路结束部分
    const roadEnd = Texture.from('road-end.png');
    const roadEndSprite = new Sprite(roadEnd);
    const roadEndRatio = roadEnd.width / roadEnd.height;
    this.roadEnd = {
      name: 'road-end',
      width: roadEnd.width,
      height: roadEnd.height,
      ratio: roadEndRatio,
      sprite: roadEndSprite,
    };
    this.container.addChild(roadEndSprite);

    this.initDragMap();
    this.resize();
  }

  // 车道
  private createLane() {
    const engine = getEngine();
    const laneSize = 250;
    const laneContainer = new Container();

    // 马路
    const lane = new Graphics();
    lane.rect(0, 0, laneSize, engine.screen.height).fill(0x716c69);
    laneContainer.addChild(lane);


    // 井盖
    const hatchScale = 0.7;
    const hatch = Texture.from('hatch_1.png');
    const hatchSprite = new Sprite(hatch);
    hatchSprite.x = (laneSize - laneSize * hatchScale) * 0.5;
    hatchSprite.y = engine.screen.height - laneSize * 1.4;
    hatchSprite.width = laneSize * hatchScale;
    hatchSprite.height = laneSize * hatchScale;
    laneContainer.addChild(hatchSprite);

    // 使用 renderer.generateTexture 生成纹理
    const laneTexture = engine.renderer.generateTexture(laneContainer);
    return new Sprite(laneTexture);
  }

  // 车道线
  private createLaneLine() {
    const engine = getEngine();
    const laneLineCount = 10;
    const laneLineWidth = 8;
    const laneLineHeight = engine.screen.height / laneLineCount;
    
    const laneLine = new Graphics();

    laneLine.rect(0, 0, laneLineWidth, laneLineHeight).fill(0x716c69);
    laneLine.rect(0, laneLineHeight * 0.25, laneLineWidth, laneLineHeight * 0.5).fill(0xd3cfc9);

    const laneLineTexture = engine.renderer.generateTexture(laneLine);
    const laneLineSprite = new TilingSprite(laneLineTexture);

    // laneLineSprite.height = engine.screen.height + laneLineHeight;
    // laneLineSprite.y = -laneLineHeight * 0.5;

    return laneLineSprite;
  }

  // 初始化地图拖拽
  private initDragMap() {
    const engine = getEngine();

    this.container.interactive = true;

    this.container.on('pointerdown', e => {
      this.dragging = true;
      this.daragX = e.x;
    });

    this.container.addEventListener('pointermove', (e) => {
      if (!this.dragging) return;
      const offset = e.x - this.daragX;
      const move = Math.max(Math.min(0, this.container.x += offset), engine.screen.width - this.container.width);
      this.container.x = move;
      this.daragX = e.x;
    });

    this.container.addEventListener('pointerup', (e) => {
      this.dragging = false;
    });

    this.container.addEventListener('pointerleave', (e) => {
      this.dragging = false;
    });

    this.container.addEventListener('pointercancel', (e) => {
      this.dragging = false;
    });
  }

  /** Update the screen */
  public update(_time: Ticker) {}

  /** Pause gameplay - automatically fired when a popup is presented */
  public async pause() {
    this.container.interactiveChildren = false;
    this.paused = true;
  }

  /** Resume gameplay */
  public async resume() {
    this.container.interactiveChildren = true;
    this.paused = false;
  }

  /** Fully reset */
  public reset() {}

  /** Resize the screen, fired whenever window size changes */
  public resize() {
    const engine = getEngine();

    const { height } = engine.renderer;

    let x = 0;

    // 调整马路开始部分
    this.roadStart.sprite.x = x;
    this.roadStart.sprite.width = height * this.roadStart.ratio;
    this.roadStart.sprite.height = height;
    x += this.roadStart.sprite.width;

    for(let i = 0; i < this.laneCount; i++) {
      // 调整车道
      const lane = this.lanes[i];
      lane.sprite.x = x;
      lane.sprite.width = height * lane.ratio;
      lane.sprite.height = height;
      x += lane.sprite.width;

      // 调整车道线
      if (i < this.laneCount - 1) {
        const laneLine = this.laneLines[i];
        const scale = height / laneLine.sprite.height;
        laneLine.sprite.x = x;
        laneLine.sprite.width = height * laneLine.ratio;
        laneLine.sprite.height = height;
        // laneLine.sprite.scale.set(scale);
        x += laneLine.sprite.width;
      }
    }

    // 调整马路结束部分
    this.roadEnd.sprite.x = x;
    this.roadEnd.sprite.width = height * this.roadEnd.ratio;
    this.roadEnd.sprite.height = height;
    this.container.addChild(this.roadEnd.sprite);
    x += this.roadEnd.sprite.width;
  }

  /** Show screen with animations */
  public async show(): Promise<void> {}

  /** Hide screen with animations */
  public async hide() {}

  /** Auto pause the app when window go out of focus */
  public blur() {}
}
