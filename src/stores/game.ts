import {ref} from "vue";
import {defineStore} from "pinia";
import {GameStatus} from "@/constant";

export const useGameStore = defineStore("game", () => {
    const isSocketOnline = ref(false);
    const setIsSocketOnline = (isOnline: boolean) => {
        isSocketOnline.value = isOnline;
    }

    // 服务器时间
    const serverTime = ref<number>(0);
    const setServerTime = (time: number) => {
        serverTime.value = time;
    }

    // 游戏状态
    const gameStatus = ref<GameStatus>();
    const setGameStatus = (status: GameStatus) => {
        gameStatus.value = status;
    }

    // 游戏id
    const gameId = ref<string>("");
    const setGameId = (id: string) => {
        gameId.value = id;
    }

    // 期数
    const gameRoundId = ref<string>("000");
    const setGameRoundId = (id: string) => {
        gameRoundId.value = id;
    }

    // 当前期是否投注过
    const isBetted = ref(false);
    const setIsBetted = (val: boolean) => {
        isBetted.value = val;
    }

    // 游戏投注结束时间
    const gameBetEndTime = ref<number>();
    /**
     * 更新游戏投注结束时间（本地时间）
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameBetEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameBetEndTime.value = Date.now() + diff;
        } else {
            gameBetEndTime.value = undefined;
        }
    }
    /**
     * 清除游戏投注结束时间
     */
    const cleanGameBetEndTime = () => {
        gameBetEndTime.value = undefined;
    }

    // 等待下一场游戏开始间隔时间
    const gameNextWaitEndTime = ref<number>();
    /**
     * 更新下一场游戏开始需要等待的时间
     * @param curr 服务器当前时间
     * @param end 服务器结束时间
     */
    const setGameNextWaitEndTime = (curr: number, end: number) => {
        const diff = end - curr;
        if (diff > 0) {
            gameNextWaitEndTime.value = Date.now() + diff;
        } else {
            gameNextWaitEndTime.value = undefined;
        }
    }
    /**
     * 清除下一场游戏开始需要等待的时间
     */
    const cleanGameNextWaitEndTime = () => {
        gameNextWaitEndTime.value = undefined;
    }

    return {
        isSocketOnline, setIsSocketOnline,
        gameStatus, setGameStatus,
        gameId, setGameId,
        gameRoundId, setGameRoundId,
        isBetted, setIsBetted,
        gameBetEndTime, setGameBetEndTime, cleanGameBetEndTime,
        gameNextWaitEndTime, setGameNextWaitEndTime, cleanGameNextWaitEndTime,
    };
});