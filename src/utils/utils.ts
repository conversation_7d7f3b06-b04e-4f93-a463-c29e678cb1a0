import CryptoJS from 'crypto-js';
import {utils} from "animejs";
import {storeToRefs} from "pinia";
import {useMainStore} from "@/stores";


export const glog = (...args: any[]) => {
  const [msg, path, ...attrs] = args;
  console.log(
    `%c[glog][${getTimeText()}] ===>%c${msg}%c${path ? `[${path}]` : ''}`,
    'background-color: #254D70; color: #4CAF50; font-weight: bold; padding: 2px 4px; border-radius: 2px;',
    'background-color: #98A1BC; color: #EAEFEF; font-weight: bold; padding: 2px 4px; border-radius: 2px; margin: 0 4px;',
    'background-color: #F2F2F2; color: #000000; font-weight: bold; padding: 2px 4px; border-radius: 2px;',
    ...attrs
  );
}

export const getTimeText = (time?: number) => {
  const d =  time ? new Date(time) : new Date();
  const pad2 = utils.padStart(2, '0');
  const pad3 = utils.padStart(3, '0');
  const h = pad2(d.getHours());
  const m = pad2(d.getMinutes());
  const s = pad2(d.getSeconds());
  const ms = pad3(d.getMilliseconds());
  return `${h}:${m}:${s}.${ms}`;
}

export const getCountDownText = (ms: number) => {
  if (ms <= 0) return "00:00:00";

  const pad2 = utils.padStart(2, '0');

  const m = Math.ceil(ms / 1000);
  const hours = Math.floor(m / 3600);
  const minutes = Math.floor(m % 3600 / 60);
  const seconds = Math.floor(m % 60);

  return `${ pad2(hours) }:${ pad2(minutes) }:${ pad2(seconds) }`;
}

export const formatAmountNoRounding = (amount: number, len = 2, isFixed = false) => {
  // 将数字转换为字符串，保留所有小数位
  let str = amount.toString();
  // 分割整数和小数部分
  let [integerPart, decimalPart = ''] = str.split('.');

  // 截断小数位数
  let formattedDecimal = decimalPart.slice(0, len);
  
  if (isFixed) {
    // 处理小数部分
    formattedDecimal = formattedDecimal.padEnd(len, '0');
  } else {
    // 抹掉尾部的0
    formattedDecimal = formattedDecimal.replace(/0+$/, '');
  }

  // 返回格式化结果
  return formattedDecimal ? `${integerPart}.${formattedDecimal}` : integerPart;
}

export const formatAmount = (amount: number | string, len = 2, isFixed = false, isSymbol = false) => {
  if (typeof amount !== 'number') {
    amount = Number(amount);
    if (isNaN(amount)) {
      const str = formatAmountNoRounding(0, len, isFixed);
      return isSymbol ? '+' + str : str;
    }
  }

  const symbol = amount >= 0 && isSymbol ? '+' : '';

  const absAmount = Math.abs(amount);

  const units = [
    { value: 1e9, suffix: 'B' },
    { value: 1e6, suffix: 'M' },
    { value: 1e3, suffix: 'K' },
  ];

  for (const unit of units) {
    if (absAmount >= unit.value) {
      const formatted = formatAmountNoRounding(amount / unit.value, len, isFixed);
      return `${symbol}${formatted}${unit.suffix}`;
    }
  }

  return `${symbol}${formatAmountNoRounding(amount, len, isFixed)}`;
}

export const formatBalance = (amount: number | string): string => {
  if (typeof amount !== 'number') {
    amount = Number(amount);
    if (isNaN(amount)) {
      return '0';
    }
  }

  const absAmount = Math.abs(amount);

  const units = [
    { value: 1e9, suffix: 'B' },
    { value: 1e6, suffix: 'M' },
    { value: 1e3, suffix: 'K' },
  ];

  for (const unit of units) {
    if (absAmount > unit.value) {
      const formatted = formatAmountNoRounding(amount / unit.value, 2, false);
      return `${formatted}${unit.suffix}`;
    }
  }

  return formatAmountNoRounding(amount, 2, false);
}

export const amountProfitColor = (amount: number | string) => {
  if (Number(amount) >= 0) return '#00c82b';
  return '#ff003f';
}

export const roundNumber = (num: number) => {
  return Math.round(num * 1e4) / 1e4;
}

export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUA = /mobile|android|iphone|ipod|blackberry|windows phone/i.test(userAgent);
  const isSmallScreen = window.innerWidth <= 768 || window.screen.width <= 768;
  return isMobileUA || isSmallScreen || isTouchDevice();
}

export const getClientX = (evt: TouchEvent | MouseEvent) => {
  const { isRotation } = storeToRefs(useMainStore());
  const evtField = isRotation.value ? 'clientY' : 'clientX'
  if (evt.type.startsWith('touch')) {
    return (evt as TouchEvent).touches[0]?.[evtField];
  }
  return (evt as MouseEvent)?.[evtField];
}

export const getTargetViewBoundingClientRect = (target: Element, isScale = true) => {
  const { scale, rotateDir } = storeToRefs(useMainStore());
  const targetRect = target.getBoundingClientRect();

  const s = isScale ? scale.value : 1;

  const width = roundNumber(targetRect.width / s);
  const height = roundNumber(targetRect.height / s);
  const left = roundNumber(targetRect.left / s);
  const top = roundNumber(targetRect.top / s);
  const bottom = roundNumber(targetRect.bottom / s);
  const right = roundNumber(targetRect.right / s);

  if (rotateDir.value === 'right') {
    // 向右旋转90度的坐标转换
    return {
      width: height,                     // 宽度变成原来的高度
      height: width,                     // 高度变成原来的宽度
      left: top,                         // 新的left = 原来的top
      top: width - right,                // 新的top = 容器宽度 - 原来的right
      bottom: width - left,              // 新的bottom = 容器宽度 - 原来的left
      right: bottom,                     // 新的right = 原来的bottom
    }
  }

  if (rotateDir.value === 'left') {
    // 向左旋转90度的坐标转换
    return {
      width: height,                     // 宽度变成原来的高度
      height: width,                     // 高度变成原来的宽度
      left: bottom,                      // 新的left = 原来的bottom
      top: left,                         // 新的top = 原来的left
      bottom: top,                       // 新的bottom = 原来的top
      right: width - bottom,             // 新的right = 容器宽度 - 原来的bottom
    }
  }

  return { width, height, left, top, bottom, right };
}

export const getRootViewBoundingClientRect = (isScale = true) => {
  const root = document.querySelector('#view-box');
  if (!root) return { width: 0, height: 0, left: 0, top: 0, bottom: 0, right: 0 };
  return getTargetViewBoundingClientRect(root, isScale);
}

export const getBoundingClientRect = (target: Element, isScale = true) => {
  const root = document.querySelector('#view-box');
  if (!root) return { width: 0, height: 0, left: 0, top: 0, bottom: 0, right: 0 };
  const { scale, rotateDir } = storeToRefs(useMainStore());
  const rootRect = root.getBoundingClientRect();
  const targetRect = target.getBoundingClientRect();

  const s = isScale ? scale.value : 1;

  const width = roundNumber(targetRect.width / s);
  const height = roundNumber(targetRect.height / s);
  const left = roundNumber((targetRect.left - rootRect.left) / s);
  const top = roundNumber((targetRect.top - rootRect.top) / s);
  const bottom = roundNumber((targetRect.bottom - rootRect.top) / s);
  const right = roundNumber((targetRect.right - rootRect.left) / s);

  if (rotateDir.value === 'right') {
    // 向右旋转90度的坐标转换
    // 当界面向右旋转90度时，坐标系统发生如下变化：
    // - 原来的X轴变成新的Y轴
    // - 原来的Y轴变成新的X轴（方向相反）
    const containerWidth = roundNumber(rootRect.width / s);

    return {
      width: height,                     // 宽度变成原来的高度
      height: width,                     // 高度变成原来的宽度
      left: top,                         // 新的left = 原来的top
      top: containerWidth - right,       // 新的top = 容器宽度 - 原来的right
      bottom: containerWidth - left,     // 新的bottom = 容器宽度 - 原来的left
      right: bottom,                     // 新的right = 原来的bottom
    }
  }

  if (rotateDir.value === 'left') {
    // 向左旋转90度的坐标转换
    // 当界面向左旋转90度时，坐标系统发生如下变化：
    // - 原来的X轴变成新的Y轴（方向相反）
    // - 原来的Y轴变成新的X轴
    const containerHeight = roundNumber(rootRect.height / s);

    return {
      width: height,                     // 宽度变成原来的高度
      height: width,                     // 高度变成原来的宽度
      left: containerHeight - bottom,    // 新的left = 容器高度 - 原来的bottom
      top: left,                         // 新的top = 原来的left
      bottom: left,                      // 新的bottom = 原来的left
      right: containerHeight - top,      // 新的right = 容器高度 - 原来的top
    }
  }

  return { width, height, left, top, bottom, right };
}

export const openFullscreen = () => {
  const view = document.querySelector("#view-box");
  view?.requestFullscreen?.().then(() => {
    // 全屏后锁定横屏展示
    (screen.orientation as any)?.lock('landscape');
  });
}

export const closeFullscreen = () => {
  document.exitFullscreen().then(() => {
    // 退出全屏后解锁屏幕方向
    screen.orientation?.unlock();
  });
}

export const rotate90Swiper = (swiper: any) => {
  const { rotateDir } = storeToRefs(useMainStore());

  swiper.___touches = {};
  swiper.___touches.currentX = swiper.touches.currentX;
  swiper.___touches.currentY = swiper.touches.currentY;

  Object.defineProperty(swiper.touches, 'currentX', {
    set: function (v) {
      if (rotateDir.value === 'none') {
        swiper.___touches.currentX = v;
      } else if (rotateDir.value === 'right') {
        swiper.___touches.currentY = v; // 旋转时将 X 坐标映射到 Y
      } else if (rotateDir.value === 'left') {
        swiper.___touches.currentY = -v; // 旋转时将 X 坐标映射到 Y
      }
    },
    get: function () {
      return swiper.___touches.currentX;
    },
  });

  Object.defineProperty(swiper.touches, 'currentY', {
    set: function (v) {
      if (rotateDir.value === 'none') {
        swiper.___touches.currentY = v;
      } else if (rotateDir.value === 'right') {
        swiper.___touches.currentX = v; // 旋转时将 Y 坐标映射到 X
      } else if (rotateDir.value === 'left') {
        swiper.___touches.currentX = -v; // 旋转时将 Y 坐标映射到 X
      }
    },
    get: function () {
      return swiper.___touches.currentY;
    },
  });
}

export const getKey = (keyStr: string) => {
  // 1. 计算 MD5 哈希
  const md5Hash = CryptoJS.MD5(keyStr).toString(); // 得到 32 位 hex 字符串

  // 2. 截取前 16 个字符作为 key 和 iv
  const keyPart = md5Hash.substring(0, 16);
  const ivPart = md5Hash.substring(0, 16);

  // 3. 转换为 CryptoJS WordArray
  const key = CryptoJS.enc.Utf8.parse(keyPart);
  const iv = CryptoJS.enc.Utf8.parse(ivPart);

  return { key, iv };
}

export const decrypt = (k: string, value: string) => {
  const { key, iv } = getKey(k);

  // 4. Base64 解码加密数据 (CryptoJS expects Base64 input directly)
  const cipherWordArray = CryptoJS.enc.Base64.parse(value);

  // 5. 执行解密
  const decrypted = CryptoJS.AES.decrypt(
    { ciphertext: cipherWordArray } as CryptoJS.lib.CipherParams,
    key,
    { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
  );

  // 6. 转换为明文字符串
  try {
    return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
  } catch (err) {
    console.error(err);
    return {}
  }
}

export const encrypt = (k: string, value: any) => {
  const { key, iv } = getKey(k);

  try {
    const encrypted = CryptoJS.AES.encrypt(
      JSON.stringify(value),
      key,
      { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
    );

    return encrypted.toString();
  } catch (e) {
    console.error(e);
    return "";
  }
}
