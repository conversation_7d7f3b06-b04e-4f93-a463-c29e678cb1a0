import mitt, { type Emitter } from 'mitt';

type GameEvent = {
  /** 连接socket初始化 */
  'socket-init': SocketData;

  /** 被挤下线 */
  'kick-out': SocketData;

  /** socket 重连失败 */
  'socket-reconnect-fail': void;
  
  /** 等待游戏可以开始下注 */
  'game-wait': SocketData;
  /** 开始下注 */
  'game-bet': SocketData;
  /** 下注结束 */
  'game-bet-end': SocketData;
  /** 游戏开始 */
  'game-start': SocketData;
  /** 游戏开始倒计时 */
  'game-start-countdown': SocketData;
  /** 游戏结束 */
  'game-end': SocketData;
  /** 游戏暂停 */
  'game-paused': SocketData;
  /** 获得比赛结果 */
  'game-settlement': SocketData;
  /** 接收中奖结果 */
  'game-award': SocketData;
  /** 各区域投注金额 */
  'game-bet-amount': SocketData;

  /** 用户投注请求 */
  'user-bet-req': EmitterCallbackData<any, string>;
  /** 用户投注响应 */
  'user-bet-res': SocketData;

  /** 用户撤销请求 */
  'user-revoke-req': EmitterCallbackData<any, string>;
  /** 用户撤销响应 */
  'user-revoke-res': SocketData;

  /** 播放音效 */
  'play-sound': string;
};

const emitter: Emitter<GameEvent> = mitt<GameEvent>();

export default emitter;