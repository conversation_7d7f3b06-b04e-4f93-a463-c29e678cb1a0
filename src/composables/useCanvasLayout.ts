import {onMounted, onUnmounted} from "vue";
import {debounce} from "radash";

/**
 * 动态计算font-size
 * @param designDraftWidth 设计稿宽度
 * @param standardSize 标准尺寸
 * @param min 最小缩放倍数
 * @param max 最大缩放倍数
 */
export const useCanvasLayout = (designDraftWidth = 375, standardSize = 10, min = 1, max = 2) => {
  let resizeObserver: ResizeObserver | null = null;

  const root = document.documentElement;
  const minFontSize = standardSize * min; /** 最小设计稿缩放大小 */
  const maxFontSize = standardSize * max; /** 最大为设计稿缩放大小 */

  const calcLayout = () => {
    const vw = window.innerWidth;
    const fontSize = vw / designDraftWidth * standardSize;
    const limitFontSize = Math.min(maxFontSize, Math.max(minFontSize, fontSize));
    root.style.fontSize = `${limitFontSize}px`;
    root.style.setProperty('--max-container', `${designDraftWidth / standardSize}rem`);
  }

  const debounceCalcLayout = debounce({ delay: 100 }, calcLayout);

  const setupResizeObserver = () => {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === root) {
          debounceCalcLayout();
        }
      }
    });

    resizeObserver.observe(root);
  }

  onMounted(() => {
    calcLayout();
    setupResizeObserver();
  });

  onUnmounted(() => {
    resizeObserver?.disconnect();
    resizeObserver = null;
  });
}