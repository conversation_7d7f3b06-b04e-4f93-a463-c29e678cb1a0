// =============================================================================
// Socket Communication Types
// =============================================================================

/**
 * Socket response data structure
 */
declare interface SocketResData {
  /** Unique message identifier */
  messageId: string;
  /** Response data payload */
  data: string;
  /** API endpoint path */
  path: string;
}

/**
 * Socket data structure for incoming messages
 */
declare interface SocketData {
  /** Timestamp when message was created */
  currentTimeMillis: number;
  /** Message payload data */
  data: any;
  /** Message identifier */
  msgId: string;
  /** API endpoint path */
  path: string;
  /** Source server name */
  serverName: string;
  /** Game identifier */
  gameId?: string;
}

/**
 * Generic emitter callback data structure
 */
declare interface EmitterCallbackData<T, K> {
  /** Optional data payload */
  data?: T;
  /** Optional callback function */
  callback?: (data: K) => void;
}

/**
 * Message handler function type
 */
declare type MessageHandler = (data: SocketData) => void;

// =============================================================================
// Audio System Types
// =============================================================================

/**
 * Audio configuration for sound management
 */
declare interface SoundConfig {
  /** 音频的唯一标识符，用于在音频管理器中引用特定的音频 */
  id: string;
  /** 音频文件的路径或URL（例如 'background_music.mp3'），支持MP3、OGG等格式 */
  src: string;
  /** 是否循环播放音频，默认为 false。常用于背景音乐（true）或一次性音效（false） */
  loop?: boolean;
  /** 音频的初始音量，范围 0.0 到 1.0，默认为 1.0。用于设置默认播放音量 */
  volume?: number;
  /** 音频池大小，指定并发播放的音频实例数，默认为 1。适用于频繁触发的音效（如跳跃音效） */
  poolSize?: number;
}

// =============================================================================
// Global Types
// =============================================================================

declare type ScreenOrientationType = 'portrait-primary' | 'portrait-secondary' | 'landscape-primary' | 'landscape-secondary';