export const VERTICAL_RATIO = 0.7;

export const DESKTOP_SCREEN_WIDTH = 667;

export const DESKTOP_SCREEN_HEIGHT = 375;

export const MOBILE_SCREEN_WIDTH = 375;

export const MOBILE_SCREEN_HEIGHT = 668;

export enum SYSTEM_ERROR_CODE {
  /** 无错误 */
  NONE = 0,
  /** 系统维护 */
  SYSTEM_MAINTAIN = 1,
  /** 网络错误 */
  NETWORK_ERROR = 2,
}

export enum SocketEventCode {
  /** 心跳-发送 */
  PING = "2000",
  /** 心跳-接收 */
  PONG = "2001",
}

export enum GameStatus {
  // The game is waiting to start, typically in an idle or lobby state.
  GAME_WAIT = "game-wait",
  // The game is in the betting phase, allowing players to place their bets.
  GAME_BET = "game-bet",
  // The betting phase has ended, and no further bets can be placed.
  GAME_BET_END = "game-bet-end",
  // The game has started, transitioning from preparation to active play.
  GAME_START = "game-start",
  // The game has concluded, but final actions like scoring or settlement may still occur.
  GAME_END = "game-end",
  // The game is in the settlement phase, where results are finalized and payouts are processed.
  GAME_SETTLEMENT = "game-settlement",
}
