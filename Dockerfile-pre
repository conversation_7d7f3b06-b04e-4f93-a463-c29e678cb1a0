# build stage
FROM node:20-alpine as build-stage

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install --prefer-offline

# Copy source code
COPY . .

# Build project
RUN npm run build-only:pre

# production stage
FROM node:20-alpine as production-stage

WORKDIR /app

# Copy built files from build stage
COPY --from=build-stage /app/dist /app

# Install serve globally
RUN npm install -g serve

# Expose port 3000
EXPOSE 3000

# Start static file server
CMD ["serve", "-s", "/app", "-l", "3000"]
