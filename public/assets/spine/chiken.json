{"skeleton": {"hash": "qsA0h9uoJOM", "spine": "4.2.33", "x": -114.67, "y": 1.2, "width": 193, "height": 222, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -16.47, "y": -10.4, "color": "9c9c9c6f"}, {"name": "bone2", "parent": "bone", "length": 47.95, "rotation": 90, "x": 0.58, "y": 67.59, "color": "ffffff6f"}, {"name": "bone3", "parent": "bone2", "rotation": 90, "x": 99.94, "y": -16.18, "inherit": "onlyTranslation", "color": "ffffff6f"}, {"name": "bone4", "parent": "bone2", "length": 51.31, "rotation": 172.23, "x": 9.24, "y": 32.93, "inherit": "onlyTranslation", "color": "ffffff6f"}, {"name": "bone5", "parent": "bone2", "length": 42.27, "rotation": -169.77, "x": 56.04, "y": -83.19, "inherit": "onlyTranslation", "color": "ffffff6f"}, {"name": "bone6", "parent": "bone", "length": 31.06, "rotation": 81, "x": -11.55, "y": 30.62, "inherit": "onlyTranslation", "color": "ffc4046f"}, {"name": "bone7", "parent": "bone6", "length": 20.35, "rotation": -6.52, "x": 0.3, "y": -5.22, "inherit": "onlyTranslation", "color": "ffc4046f"}, {"name": "bone8", "parent": "bone", "length": 42.18, "rotation": -250.02, "x": 27.14, "y": 48.12, "inherit": "onlyTranslation", "color": "ffc4046f"}, {"name": "bone9", "parent": "bone8", "length": 25.79, "rotation": -7.69, "x": -4.25, "y": -5.66, "inherit": "onlyTranslation", "color": "ffc4046f"}, {"name": "bone10", "parent": "bone3", "length": 40.9, "rotation": 143.62, "x": 32.93, "y": 7.51, "inherit": "onlyTranslation", "color": "ff0b0b6f"}, {"name": "bone11", "parent": "bone10", "length": 24.14, "rotation": 158.96, "x": 22.25, "y": 21.42, "inherit": "onlyTranslation", "color": "ff0b0b6f"}, {"name": "bone12", "parent": "bone11", "length": 17.67, "rotation": 168.69, "x": 14.43, "y": 20.41, "inherit": "onlyTranslation", "color": "ff0b0b6f"}, {"name": "bone13", "parent": "bone3", "rotation": 90, "x": -20.22, "y": -9.24, "inherit": "onlyTranslation", "color": "ffc4046f"}, {"name": "bone14", "parent": "bone13", "length": 18.27, "rotation": -55.3, "x": 11.55, "y": -6.93, "inherit": "noScaleOrReflection", "color": "ffc4046f"}, {"name": "bone15", "parent": "bone13", "length": 13.96, "rotation": -114.44, "x": -8.67, "y": -6.93, "inherit": "noScaleOrReflection", "color": "ffc4046f"}, {"name": "bone16", "parent": "bone13", "length": 21.3, "rotation": -229.4, "x": 1.16, "y": -14.44, "inherit": "noScaleOrReflection", "color": "ff01016f"}, {"name": "bone17", "parent": "bone16", "length": 16.76, "rotation": 47.42, "x": 23.62, "y": 1.82, "inherit": "noScaleOrReflection", "color": "ff01016f"}, {"name": "bone18", "parent": "bone3", "rotation": 90, "x": -2.31, "y": 25.42, "inherit": "onlyTranslation", "color": "fff5ab6f"}, {"name": "bone19", "parent": "bone18", "rotation": 90, "x": -1.73, "y": 16.75, "inherit": "onlyTranslation", "color": "2525256f"}, {"name": "bone20", "parent": "bone3", "rotation": 90, "x": 5.78, "y": -39.86, "inherit": "onlyTranslation", "color": "fff5ab6f"}, {"name": "bone21", "parent": "bone20", "rotation": 90, "y": -10.98, "inherit": "onlyTranslation", "color": "2525256f"}, {"name": "bone22", "parent": "bone2", "length": 29.52, "rotation": 120.58, "x": 54.3, "y": 67.01, "inherit": "onlyTranslation", "color": "ffffff6f"}, {"name": "bone23", "parent": "bone", "x": -0.71, "y": 58.54, "color": "9c9c9c6f", "icon": "circle"}, {"name": "bone24", "parent": "bone18", "length": 13.78, "rotation": -178.41, "x": 17.28, "y": 5.89, "color": "fff5ab6f"}, {"name": "bone25", "parent": "bone18", "length": 10.15, "rotation": 2.16, "x": -32.27, "y": 4.17, "color": "fff5ab6f"}, {"name": "bone26", "parent": "bone18", "x": -17.92, "y": 7.04, "color": "fff5ab6f"}, {"name": "bone27", "parent": "bone20", "length": 16.35, "rotation": -173.96, "x": 16.65, "y": 4.4, "color": "fff5ab6f"}, {"name": "bone28", "parent": "bone20", "length": 10.95, "rotation": 5.01, "x": -24.67, "y": -4.78, "color": "fff5ab6f"}, {"name": "bone29", "parent": "bone20", "x": -11.28, "y": -4.97, "color": "fff5ab6f"}, {"name": "bone30", "parent": "root", "x": -19.4, "y": -487.12}, {"name": "bone31", "parent": "bone30", "length": 117.85, "rotation": 101.78, "y": 72.21}, {"name": "bone32", "parent": "bone31", "length": 36.16, "rotation": -2.54, "x": 99.3, "y": -19.87}, {"name": "bone33", "parent": "bone31", "length": 68.37, "rotation": -44.9, "x": 125.76, "y": -67.78}, {"name": "bone34", "parent": "bone31", "length": 70.36, "rotation": 61.06, "x": 132.22, "y": 44.48}, {"name": "bone35", "parent": "bone31", "length": 27.44, "rotation": -98.32, "x": 75.88, "y": -75.18}, {"name": "bone36", "parent": "bone31", "length": 31.71, "rotation": 72.21, "x": 75.42, "y": 64.81}, {"name": "bone37", "parent": "bone31", "length": 37.94, "rotation": -1.7, "x": -5.68, "y": -75.97}, {"name": "bone38", "parent": "bone31", "length": 44.7, "rotation": 19.54, "x": -29.07, "y": 47.61}, {"name": "bone39", "parent": "root", "length": 178, "rotation": 90, "x": 425.27, "y": 20.01}, {"name": "bone40", "parent": "root", "length": 133.36, "rotation": 83.25, "x": 488.98, "y": 13.18}, {"name": "bone41", "parent": "root", "length": 133.36, "rotation": 83.25, "x": 448.31, "y": 24.27, "scaleX": 0.4537, "scaleY": 0.4537}, {"name": "bone42", "parent": "root", "length": 178, "rotation": 90, "x": 532.53, "y": 14.22, "scaleX": 0.4537, "scaleY": 0.4537}, {"name": "bone43", "parent": "root", "x": -15.64, "y": 236.49}, {"name": "bone44", "parent": "bone43", "x": -0.56, "y": 100.79}, {"name": "bone45", "parent": "bone43", "x": -13.23, "y": 84.82}, {"name": "bone46", "parent": "bone43", "x": 3.02, "y": 121.95}, {"name": "bone47", "parent": "bone43", "x": 20.03, "y": 77.86}, {"name": "bone48", "parent": "bone43", "x": -26.38, "y": 128.92}, {"name": "bone49", "parent": "bone43", "rotation": 1.74, "x": 60.37, "y": 135.75}, {"name": "bone50", "parent": "bone43", "x": 69.9, "y": 84.01}, {"name": "bone51", "parent": "bone43", "x": 52.88, "y": 128.1}, {"name": "bone52", "parent": "bone43", "x": 36.64, "y": 90.97}, {"name": "bone53", "parent": "bone43", "x": 130.49, "y": 92.91}, {"name": "bone54", "parent": "bone43", "x": 34.69, "y": 53.31}, {"name": "bone55", "parent": "root", "x": 168.28, "y": -240.2, "color": "ff0000ff", "icon": "circle"}, {"name": "bone56", "parent": "bone55", "length": 149.22, "rotation": 90, "x": 1.51, "y": 76.87}, {"name": "bone57", "parent": "bone55", "length": 164.32, "rotation": 91.05, "y": 346.67}, {"name": "bone58", "parent": "bone57", "length": 36.02, "rotation": -169.74, "x": 130.27, "y": -10.46}, {"name": "bone59", "parent": "bone58", "length": 33.31, "rotation": -10.15, "x": 42.02, "y": -0.86}, {"name": "bone60", "parent": "bone59", "length": 34.11, "rotation": -6.25, "x": 40.7, "y": -0.49}, {"name": "bone61", "parent": "bone56", "length": 41.82, "rotation": 4.15, "x": 118.36, "y": -2.87}, {"name": "bone62", "parent": "bone61", "length": 46.82, "rotation": -7.03, "x": 46.57, "y": 0.33}, {"name": "bone63", "parent": "bone62", "length": 44.53, "rotation": -1.45, "x": 55.25, "y": -0.25}], "slots": [{"name": "988998675654", "bone": "bone57"}, {"name": "778887899898", "bone": "bone58"}, {"name": "shadow", "bone": "bone23", "attachment": "shadow"}, {"name": "Layer 20", "bone": "bone2", "attachment": "Layer 20"}, {"name": "Layer 19", "bone": "bone20", "attachment": "Layer 19"}, {"name": "Layer 18", "bone": "bone5", "attachment": "Layer 18"}, {"name": "Layer 17", "bone": "bone8", "attachment": "Layer 17"}, {"name": "Layer 16", "bone": "bone6", "attachment": "Layer 16"}, {"name": "Layer 15", "bone": "bone4", "attachment": "Layer 15"}, {"name": "Layer 14", "bone": "bone10", "attachment": "Layer 14"}, {"name": "Layer 13", "bone": "bone8", "attachment": "Layer 13"}, {"name": "Layer 12", "bone": "bone6", "attachment": "Layer 12"}, {"name": "wing-down", "bone": "bone5", "attachment": "wing-down"}, {"name": "crest", "bone": "bone10", "attachment": "crest"}, {"name": "body", "bone": "bone2", "attachment": "body"}, {"name": "Layer 11", "bone": "bone13", "attachment": "Layer 11"}, {"name": "Layer 4", "bone": "bone20", "attachment": "Layer 4"}, {"name": "Layer 3", "bone": "bone18", "attachment": "Layer 3"}, {"name": "Layer 8", "bone": "bone18", "attachment": "Layer 8"}, {"name": "Layer 7", "bone": "bone20", "attachment": "Layer 7"}, {"name": "Layer 6", "bone": "bone18", "attachment": "Layer 6"}, {"name": "Layer 9", "bone": "bone19", "attachment": "Layer 9"}, {"name": "Layer 5", "bone": "bone20", "attachment": "Layer 5"}, {"name": "Layer 10", "bone": "bone21", "attachment": "Layer 10"}, {"name": "wing-up", "bone": "bone4", "attachment": "wing-up"}, {"name": "Layer 5 copy", "bone": "bone27"}, {"name": "Layer 25", "bone": "bone28"}, {"name": "Layer 24", "bone": "bone29"}, {"name": "mouth-inside", "bone": "bone13", "attachment": "mouth-inside"}, {"name": "beak-down", "bone": "bone13", "attachment": "beak-down"}, {"name": "yazik", "bone": "bone13", "attachment": "yazik"}, {"name": "beak-up", "bone": "bone13", "attachment": "beak-up"}, {"name": "Layer 6 copy", "bone": "bone24"}, {"name": "Layer 23", "bone": "bone25"}, {"name": "Layer 22", "bone": "bone26"}, {"name": "1111122323232", "bone": "bone31"}, {"name": "2211132333", "bone": "bone37"}, {"name": "11112222211", "bone": "bone34"}, {"name": "22231113344", "bone": "bone38"}, {"name": "1111122222333", "bone": "bone33"}, {"name": "1111122223344", "bone": "bone32"}, {"name": "1112223333434", "bone": "bone36"}, {"name": "111122223333444", "bone": "bone35"}, {"name": "Group 1000006385 6", "bone": "bone39"}, {"name": "Group 1000006385 7", "bone": "bone42"}, {"name": "Group 1000006386", "bone": "bone40"}, {"name": "Group 1000006387", "bone": "bone41"}, {"name": "123122331112334", "bone": "bone44"}, {"name": "Layer 45555555", "bone": "bone45"}, {"name": "Layer 45555556", "bone": "bone52"}, {"name": "Layer 55555555", "bone": "bone47"}, {"name": "Layer 55555556", "bone": "bone50"}, {"name": "Layer 533333333", "bone": "bone48"}, {"name": "Layer 533333334", "bone": "bone49"}, {"name": "Layer 655555555", "bone": "bone46"}, {"name": "Layer 655555556", "bone": "bone51"}, {"name": "Layer 1212", "bone": "bone53"}, {"name": "Layer 1213", "bone": "bone54"}, {"name": "3344555656556", "bone": "bone61"}, {"name": "566778878990", "bone": "bone56"}, {"name": "11112222211234", "bone": "bone20"}, {"name": "1111122222333123", "bone": "bone18"}, {"name": "$", "bone": "bone19"}, {"name": "$2", "bone": "bone21"}], "skins": [{"name": "default", "attachments": {"2211132333": {"2211132333": {"x": 6.96, "y": -2.52, "rotation": -100.08, "width": 65, "height": 55}}, "$": {"$": {"x": 3.41, "y": -1.25, "rotation": -90, "width": 44, "height": 60}}, "$2": {"$": {"x": 5.08, "y": -1.82, "rotation": -90, "width": 44, "height": 60}}, "11112222211": {"11112222211": {"type": "mesh", "uvs": [0.80396, 0.16557, 0.90073, 0.41321, 1, 0.59308, 1, 0.72014, 0.90588, 0.82027, 0.76098, 0.88445, 0.55318, 0.99129, 0.2394, 0.97386, 0.0419, 0.77983, 1e-05, 0.34377, 0.19997, 0.05793, 0.57691, 1e-05], "triangles": [4, 1, 2, 3, 4, 2, 5, 6, 1, 7, 8, 1, 1, 6, 7, 9, 10, 1, 5, 1, 4, 0, 10, 11, 1, 10, 0, 1, 8, 9], "vertices": [1, 34, 24.33, -33.32, 1, 1, 34, 11.64, -17.8, 1, 1, 31, 138.65, 41.23, 1, 1, 31, 129.32, 43.18, 1, 1, 31, 123.47, 51.9, 1, 1, 34, 11.63, 19.18, 1, 1, 34, 24.76, 31.62, 1, 1, 34, 48.53, 37.59, 1, 1, 34, 67.54, 28.23, 1, 1, 34, 80.31, -2.06, 1, 1, 34, 71.73, -27.14, 1, 1, 34, 44.92, -39.97, 1], "hull": 12, "edges": [0, 22, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 2, 2, 4, 8, 10, 10, 12, 2, 10, 4, 8], "width": 104, "height": 101}}, "22231113344": {"22231113344": {"x": 23.22, "y": 3.84, "rotation": -121.33, "width": 65, "height": 56}}, "566778878990": {"566778878990": {"x": 54.87, "y": 0.56, "rotation": -90, "width": 91, "height": 232}}, "778887899898": {"778887899898": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.88889, 0, 0.77778, 0, 0.66667, 0, 0.55556, 0, 0.44444, 0, 0.33333, 0, 0.22222, 0, 0.11111, 0, 0, 1, 0, 1, 0.11111, 1, 0.22222, 1, 0.33333, 1, 0.44444, 1, 0.55556, 1, 0.66667, 1, 0.77778, 1, 0.88889], "triangles": [18, 3, 17, 19, 2, 18, 0, 1, 19, 3, 4, 17, 2, 3, 18, 1, 2, 19, 4, 5, 16, 15, 6, 14, 16, 5, 15, 6, 7, 14, 5, 6, 15, 17, 4, 16, 7, 8, 13, 12, 9, 11, 13, 8, 12, 8, 9, 12, 14, 7, 13, 9, 10, 11], "vertices": [1, 60, 41.72, 11.02, 1, 1, 60, 44.03, -14.87, 1, 1, 60, 31.08, -16.03, 1, 1, 60, 18.13, -17.18, 1, 2, 59, 43.86, -19.28, 0.34857, 60, 5.18, -18.33, 0.65143, 1, 59, 30.86, -19.01, 1, 1, 59, 17.86, -18.75, 1, 2, 58, 43.55, -19.91, 0.29143, 59, 4.87, -18.49, 0.70857, 1, 58, 30.81, -17.36, 1, 1, 58, 18.06, -14.82, 1, 1, 57, 122.86, 0.66, 1, 1, 57, 122.39, -25.34, 1, 2, 57, 109.39, -25.1, 6e-05, 58, 23.16, 10.68, 0.99994, 1, 58, 35.9, 8.13, 1, 2, 58, 48.65, 5.58, 0.29143, 59, 5.39, 7.51, 0.70857, 1, 59, 18.39, 7.24, 1, 1, 59, 31.39, 6.98, 1, 2, 59, 44.38, 6.72, 0.34857, 60, 2.88, 7.56, 0.65143, 1, 60, 15.83, 8.72, 1, 1, 60, 28.77, 9.87, 1], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0], "width": 26, "height": 117}}, "988998675654": {"988998675654": {"x": 69.76, "y": -2.02, "rotation": -91.05, "width": 91, "height": 232}}, "1111122222333": {"1111122222333": {"type": "mesh", "uvs": [0.67155, 0.01676, 0.89369, 0.12829, 0.99724, 0.30673, 0.99448, 0.54922, 0.85182, 0.73028, 0.56567, 0.78751, 0.22253, 0.99521, 0.04412, 1, 0, 0.86755, 0.14749, 0.61538, 0.10701, 0.48491, 0.06519, 0.30482, 0.20798, 0.0543, 0.40195, 1e-05], "triangles": [7, 8, 6, 8, 9, 6, 6, 9, 5, 0, 5, 9, 12, 13, 11, 4, 5, 2, 0, 9, 13, 3, 4, 2, 0, 2, 5, 13, 9, 10, 1, 2, 0, 13, 10, 11], "vertices": [1, 33, 92.92, 9.76, 1, 1, 33, 94, -10.48, 1, 1, 33, 84.87, -26.2, 1, 1, 33, 66.27, -38.08, 1, 1, 33, 46.31, -37.64, 1, 1, 33, 29.6, -21.55, 1, 1, 31, 118.55, -73.54, 1, 1, 31, 121, -59.65, 1, 1, 31, 133.51, -58.7, 1, 1, 33, 24.67, 14.68, 1, 1, 33, 32.87, 23.85, 1, 1, 33, 44.79, 35.57, 1, 1, 33, 70.05, 38.57, 1, 1, 33, 82.56, 28.43, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 8, 10, 10, 12, 16, 12, 12, 14, 18, 10], "width": 105, "height": 121}}, "1111122223344": {"1111122223344": {"x": 4.9, "y": -2.2, "rotation": -99.25, "width": 49, "height": 78}}, "1111122323232": {"1111122323232": {"x": 60, "y": -10.13, "rotation": -101.78, "width": 172, "height": 223}}, "1112223333434": {"1112223333434": {"x": 15.74, "y": 5.48, "rotation": -173.99, "width": 39, "height": 42}}, "3344555656556": {"3344555656556": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.92857, 0, 0.85714, 0, 0.78571, 0, 0.71429, 0, 0.64286, 0, 0.57143, 0, 0.5, 0, 0.42857, 0, 0.35714, 0, 0.28571, 0, 0.21429, 0, 0.14286, 0, 0.07143, 0, 0, 1, 0, 1, 0.07143, 1, 0.14286, 1, 0.21429, 1, 0.28571, 1, 0.35714, 1, 0.42857, 1, 0.5, 1, 0.57143, 1, 0.64286, 1, 0.71429, 1, 0.78571, 1, 0.85714, 1, 0.92857], "triangles": [14, 15, 16, 17, 14, 16, 13, 14, 17, 18, 13, 17, 12, 13, 18, 19, 12, 18, 11, 12, 19, 20, 11, 19, 10, 11, 20, 21, 10, 20, 9, 10, 21, 22, 9, 21, 8, 9, 22, 23, 8, 22, 7, 8, 23, 24, 7, 23, 6, 7, 24, 25, 6, 24, 5, 6, 25, 26, 5, 25, 4, 5, 26, 27, 4, 26, 28, 3, 27, 29, 2, 28, 0, 1, 29, 3, 4, 27, 2, 3, 28, 1, 2, 29], "vertices": [1, 61, 0.84, -15.6, 1, 2, 56, 120.33, 10.64, 0.97369, 61, 2.94, 13.33, 0.02631, 2, 56, 131.76, 10.64, 0.99626, 61, 14.34, 12.5, 0.00374, 2, 56, 143.19, 10.64, 0.99907, 61, 25.74, 11.67, 0.00093, 3, 56, 154.61, 10.64, 0.51222, 61, 37.13, 10.84, 0.36937, 62, -10.65, 9.28, 0.11842, 3, 56, 166.04, 10.64, 0.03629, 61, 48.53, 10.02, 0.12451, 62, 0.77, 9.86, 0.8392, 1, 62, 12.18, 10.43, 1, 1, 62, 23.59, 11.01, 1, 2, 62, 35.01, 11.58, 0.99956, 63, -20.53, 11.31, 0.00044, 2, 62, 46.42, 12.15, 0.77446, 63, -9.14, 12.18, 0.22554, 2, 62, 57.84, 12.73, 0.13142, 63, 2.26, 13.04, 0.86858, 1, 63, 13.66, 13.9, 1, 1, 63, 25.05, 14.77, 1, 1, 63, 36.45, 15.63, 1, 1, 63, 47.84, 16.49, 1, 1, 63, 59.24, 17.36, 1, 1, 63, 61.43, -11.56, 1, 1, 63, 50.03, -12.42, 1, 1, 63, 38.64, -13.29, 1, 1, 63, 27.24, -14.15, 1, 2, 62, 70.71, -15.66, 0.00165, 63, 15.85, -15.01, 0.99835, 2, 62, 59.3, -16.23, 0.14158, 63, 4.45, -15.88, 0.85842, 2, 62, 47.88, -16.81, 0.65859, 63, -6.95, -16.74, 0.34141, 2, 62, 36.47, -17.38, 0.96985, 63, -18.34, -17.6, 0.03015, 2, 61, 69.23, -20.56, 0.00389, 62, 25.05, -17.96, 0.99611, 2, 61, 57.83, -19.73, 0.0872, 62, 13.64, -18.53, 0.9128, 2, 61, 46.43, -18.91, 0.4213, 62, 2.22, -19.11, 0.5787, 2, 61, 35.04, -18.08, 0.85895, 62, -9.19, -19.68, 0.14105, 2, 61, 23.64, -17.25, 0.99621, 62, -20.6, -20.26, 0.00379, 1, 61, 12.24, -16.42, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 0], "width": 29, "height": 160}}, "11112222211234": {"11112222211234": {"x": 35.58, "y": -29.55, "rotation": -90, "width": 97, "height": 105}}, "111122223333444": {"111122223333444": {"x": 20.37, "y": -0.61, "rotation": -3.47, "width": 35, "height": 43}}, "123122331112334": {"123122331112334": {"x": -0.38, "y": -1.08, "width": 129, "height": 124}}, "1111122222333123": {"1111122222333123": {"x": 40.75, "y": -30.89, "rotation": -90, "width": 105, "height": 121}}, "beak-down": {"beak-down": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 15, 27.05, -4.24, 1, 1, 15, -3.9, -18.31, 1, 1, 15, -15.9, 8.09, 1, 1, 15, 15.05, 22.16, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 29}}, "beak-up": {"beak-up": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 14, 8.28, -35.25, 1, 1, 13, -12.72, 7.2, 1, 2, 14, -0.95, 23.44, 0.30854, 13, 30.28, 7.2, 0.69146, 2, 14, 32.76, 0.1, 0.99996, 13, 30.28, -33.8, 4e-05], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 41, "height": 43}}, "body": {"body": {"type": "mesh", "uvs": [0.92756, 0.19729, 1, 0.46247, 1, 0.8444, 0.80391, 1, 0.34253, 1, 0.11, 0.91168, 0, 0.78503, 0, 0.57625, 0, 0.36748, 0.10446, 0.25468, 0.19489, 0.30415, 0.26133, 0.29624, 0.31669, 0.15969, 0.43481, 0, 0.77992, 0], "triangles": [1, 11, 0, 12, 0, 11, 12, 13, 14, 12, 14, 0, 11, 7, 10, 10, 7, 8, 10, 8, 9, 5, 6, 7, 7, 11, 5, 11, 1, 4, 1, 3, 4, 4, 5, 11, 2, 3, 1], "vertices": [1, 3, 2.31, -55.15, 1, 2, 2, 58.24, -84.22, 0.49683, 3, -41.7, -68.05, 0.50317, 1, 2, -5.17, -84.22, 1, 1, 2, -31, -49.32, 1, 1, 2, -31, 32.81, 1, 1, 2, -16.33, 74.2, 1, 2, 2, 5, 93.16, 0.69714, 22, -29.14, 47.6, 0.30286, 2, 2, 38.34, 94.27, 0.31429, 22, -0.62, 33.06, 0.68571, 1, 22, 30.58, 13.02, 1, 1, 22, 37.24, -12.51, 1, 1, 22, 21.98, -22.19, 1, 2, 2, 85.83, 47.26, 0.74857, 22, 17.09, -33.04, 0.25143, 1, 3, 8.56, 53.58, 1, 1, 3, 35.06, 32.56, 1, 1, 3, 35.06, -28.87, 1], "hull": 15, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 10, 12, 14, 14, 16], "width": 178, "height": 166}}, "crest": {"crest": {"type": "mesh", "uvs": [1, 0.19336, 1, 0.59668, 0.68, 0.54961, 0.63959, 0.65071, 0.5, 1, 0.19767, 1, 0, 0.75, 0, 0.625, 0, 0.5, 0.08144, 0.48897, 0.07563, 0.29568, 0.21655, 0.27673, 0.35747, 0.25778, 0.27031, 0.10619, 0.36619, 0, 0.85433, 0], "triangles": [3, 4, 9, 9, 4, 5, 3, 12, 2, 1, 2, 12, 6, 9, 5, 6, 7, 9, 7, 8, 9, 3, 9, 11, 11, 12, 3, 9, 10, 11, 1, 15, 0, 14, 15, 1, 13, 14, 1, 1, 12, 13], "vertices": [1, 10, -8.79, -26.1, 1, 2, 10, -25.3, -3.69, 0.49143, 3, 20.89, -15.05, 0.50857, 1, 3, 24.14, 13.75, 1, 1, 3, 17.17, 17.39, 1, 1, 3, -6.94, 29.95, 1, 2, 12, -6.03, 21.18, 0.64571, 3, -6.94, 57.16, 0.35429, 1, 12, 14.79, 7.76, 1, 1, 12, 16.49, -0.7, 1, 1, 12, 18.18, -9.16, 1, 2, 11, 27.33, 11.11, 0.52406, 12, 11.14, -11.34, 0.47594, 1, 11, 32.6, -1.15, 1, 1, 11, 21.24, -6.92, 1, 2, 10, 35.13, 11.79, 0.44023, 11, 9.87, -12.7, 0.55977, 1, 10, 47.65, 8.02, 1, 1, 10, 45.05, -3, 1, 1, 10, 9.68, -29.06, 1], "hull": 16, "edges": [16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 8, 10, 12, 10, 18, 8, 24, 4, 20, 22, 22, 24, 4, 6, 6, 8, 22, 6, 26, 2, 12, 14, 14, 16], "width": 90, "height": 69}}, "Group 1000006385 6": {"Group 1000006385 6": {"x": 79.42, "y": 2.78, "rotation": -90, "width": 61, "height": 221}}, "Group 1000006385 7": {"Group 1000006385 6": {"x": 79.42, "y": 2.78, "rotation": -90, "width": 61, "height": 221}}, "Group 1000006386": {"Group 1000006386": {"x": 66.47, "y": -2.54, "rotation": -83.25, "width": 46, "height": 139}}, "Group 1000006387": {"Group 1000006386": {"x": 66.47, "y": -2.54, "rotation": -83.25, "width": 46, "height": 139}}, "Layer 3": {"Layer 3": {"x": 10.38, "y": 7.03, "rotation": -90, "width": 59, "height": 46}}, "Layer 4": {"Layer 4": {"x": 8.29, "y": 8.81, "rotation": -90, "width": 30, "height": 36}}, "Layer 5": {"Layer 5": {"x": -3.21, "y": 0.81, "rotation": -90, "width": 38, "height": 51}}, "Layer 5 copy": {"Layer 5 copy": {"type": "mesh", "uvs": [1, 0.42, 1, 0.63, 1, 0.84, 0.81767, 1, 0.53206, 0.99999, 0.2707, 1, 0, 0.82524, 0, 0.53753, 0, 0.24981, 0.21905, 0, 0.67645, 0, 0.22643, 0.68507, 0.48464, 0.75885, 0.77973, 0.71458], "triangles": [11, 9, 10, 11, 8, 9, 1, 13, 0, 4, 13, 3, 11, 7, 8, 10, 12, 11, 13, 12, 10, 6, 7, 11, 5, 11, 12, 5, 12, 4, 6, 11, 5, 10, 0, 13, 4, 12, 13, 13, 1, 2, 3, 13, 2], "vertices": [2, 27, 10.74, 18.93, 0.54857, 20, 7.97, -15.55, 0.45143, 1, 27, 18.47, 18.11, 1, 1, 27, 26.19, 17.29, 1, 1, 27, 31.37, 9.96, 1, 1, 27, 30.26, -0.55, 1, 1, 27, 29.24, -10.17, 1, 1, 27, 21.75, -19.45, 1, 2, 27, 11.17, -18.33, 0.75429, 20, 3.62, 21.45, 0.24571, 1, 20, 14.26, 21.45, 1, 1, 20, 23.51, 13.35, 1, 1, 20, 23.51, -3.58, 1, 1, 27, 17.48, -10.57, 1, 1, 27, 21.2, -1.36, 1, 1, 27, 20.72, 9.67, 1], "hull": 11, "edges": [12, 10, 8, 10, 6, 8, 6, 4, 20, 0, 18, 20, 18, 16, 12, 14, 14, 16, 14, 22, 22, 24, 24, 26, 0, 2, 2, 4, 26, 2], "width": 37, "height": 37}}, "Layer 6": {"Layer 6": {"x": -5.12, "y": 4.03, "rotation": -90, "width": 57, "height": 63}}, "Layer 6 copy": {"Layer 6 copy": {"type": "mesh", "uvs": [1, 0.25601, 1, 0.50227, 1, 0.74853, 0.78898, 0.90281, 0.49719, 1, 0.2054, 1, 0, 0.90281, 0, 0.67711, 0, 0.45141, 0.26188, 0, 0.81251, 0, 0.24305, 0.77226, 0.58661, 0.74853, 0.83134, 0.61205], "triangles": [12, 9, 10, 13, 10, 0, 9, 11, 8, 1, 13, 0, 8, 11, 7, 12, 10, 13, 13, 1, 2, 9, 12, 11, 6, 7, 11, 3, 12, 13, 3, 13, 2, 5, 6, 11, 4, 11, 12, 4, 12, 3, 5, 11, 4], "vertices": [2, 24, 3.18, 31.5, 0.33143, 18, 14.98, -25.68, 0.66857, 2, 24, 14.5, 31.18, 0.62857, 18, 3.65, -25.68, 0.37143, 2, 24, 25.82, 30.87, 0.87428, 18, -7.67, -25.68, 0.12572, 2, 24, 32.58, 18.44, 0.90857, 18, -14.77, -13.44, 0.09143, 1, 24, 36.58, 1.39, 1, 2, 24, 36.11, -15.52, 0.88, 18, -19.24, 20.4, 0.12, 2, 24, 31.31, -27.31, 0.85143, 18, -14.77, 32.32, 0.14857, 2, 24, 20.93, -27.02, 0.52571, 18, -4.39, 32.32, 0.47429, 2, 24, 10.55, -26.73, 0.35429, 18, 5.99, 32.32, 0.64571, 1, 18, 26.76, 17.13, 1, 1, 18, 26.76, -14.81, 1, 2, 24, 25.7, -13.05, 0.94286, 18, -8.77, 18.22, 0.05714, 1, 24, 25.16, 6.9, 1, 2, 24, 19.28, 21.26, 0.82857, 18, -1.4, -15.9, 0.17143], "hull": 11, "edges": [12, 10, 8, 10, 8, 6, 6, 4, 16, 18, 18, 20, 20, 0, 12, 14, 14, 16, 14, 22, 22, 24, 24, 26, 0, 2, 2, 4, 26, 2], "width": 58, "height": 46}}, "Layer 7": {"Layer 7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.71, -19.19, -34.71, 14.81, -1.71, 14.81, -1.71, -19.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 33}}, "Layer 8": {"Layer 8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-44.62, -22.47, -44.62, 29.53, -1.62, 29.53, -1.62, -22.47], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 52, "height": 43}}, "Layer 9": {"Layer 9": {"x": 0.11, "y": -0.72, "rotation": -90, "width": 15, "height": 20}}, "Layer 10": {"Layer 10": {"x": 0.29, "y": -0.21, "rotation": -90, "width": 12, "height": 18}}, "Layer 11": {"Layer 11": {"x": -11.72, "y": -6.8, "rotation": -90, "width": 52, "height": 70}}, "Layer 12": {"Layer 12": {"type": "mesh", "uvs": [1, 0.29344, 0.47656, 0.71707, 0.77036, 0.68831, 1, 0.81274, 1, 0.90637, 0.78049, 1, 0.39024, 1, 0, 0.93513, 0, 0.74464, 0.5, 0, 1, 0, 0.25813, 0.82212], "triangles": [5, 2, 3, 4, 5, 3, 1, 2, 5, 6, 1, 5, 9, 10, 0, 0, 1, 9, 1, 8, 9, 11, 8, 1, 7, 8, 11, 6, 11, 1, 7, 11, 6], "vertices": [1, 6, 39.25, -25.14, 1, 2, 6, 9.35, -8.45, 0.51584, 7, 3.61, 8.9, 0.48416, 2, 6, 13.15, -20.02, 0.00702, 7, 15.34, 12.2, 0.99298, 1, 7, 25.66, 5.59, 1, 1, 7, 26.39, -0.26, 1, 2, 6, -6.14, -23.72, 0.205, 7, 18.2, -7.23, 0.795, 2, 6, -8.82, -7.94, 0.56944, 7, 2.32, -9.23, 0.43056, 2, 6, -7.47, 8.52, 0.95068, 7, -14.06, -7.17, 0.04932, 1, 6, 4.36, 10.53, 1, 1, 6, 54.05, -1.83, 1, 1, 6, 57.48, -22.04, 1, 2, 6, 1.32, -0.73, 0.92782, 7, -4.45, 1.22, 0.07218], "hull": 11, "edges": [18, 20, 18, 16, 0, 20, 0, 2, 2, 4, 4, 6, 16, 22, 22, 2, 6, 8, 10, 8, 14, 16, 10, 12, 14, 12], "width": 41, "height": 63}}, "Layer 13": {"Layer 13": {"type": "mesh", "uvs": [0.48374, 0.69211, 0.66854, 0.58248, 0.9131, 0.60681, 1, 0.72741, 1, 1, 0.57405, 1, 0.23528, 0.92064, 0, 0.20689, 0, 0, 0.28826, 0], "triangles": [5, 0, 1, 6, 0, 5, 1, 2, 3, 3, 5, 1, 4, 5, 3, 7, 8, 9, 7, 9, 0, 6, 7, 0], "vertices": [2, 8, -0.12, -8.63, 0.21545, 9, 0.71, 5.04, 0.78455, 1, 9, 8.86, 12.12, 1, 1, 9, 21.31, 13.53, 1, 1, 9, 26.83, 8.77, 1, 1, 9, 29.51, -4.04, 1, 1, 9, 8.24, -8.48, 1, 2, 8, -7.2, 6.56, 0.74724, 9, -9.45, -8.29, 0.25276, 1, 8, 29.03, 8.73, 1, 1, 8, 38.58, 6.02, 1, 1, 8, 34.57, -8.12, 1], "hull": 10, "edges": [14, 16, 14, 12, 8, 10, 12, 10, 16, 18, 18, 0, 0, 2, 2, 4, 8, 6, 4, 6, 0, 12], "width": 51, "height": 48}}, "Layer 14": {"Layer 14": {"type": "mesh", "uvs": [1, 0.20065, 1, 0.67204, 0.75022, 0.59635, 0.67007, 0.72672, 0.50206, 1, 0.21654, 1, 0, 0.77183, 0, 0.62559, 0, 0.47936, 0.08579, 0.45871, 0.09913, 0.24538, 0.19786, 0.23506, 0.29659, 0.22474, 0.32595, 0, 0.85696, 0], "triangles": [3, 4, 9, 9, 4, 5, 3, 12, 2, 6, 9, 5, 6, 7, 9, 7, 8, 9, 9, 10, 11, 3, 9, 11, 11, 12, 3, 1, 14, 0, 13, 1, 2, 13, 14, 1, 12, 13, 2], "vertices": [1, 10, -9.96, -29.56, 1, 2, 10, -31.22, -0.72, 0.53143, 3, 14.99, -18.05, 0.46857, 1, 3, 20.74, 6.43, 1, 1, 3, 10.83, 14.29, 1, 1, 3, -9.94, 30.75, 1, 2, 12, -5.08, 24.43, 0.62286, 3, -9.94, 58.73, 0.37714, 1, 12, 19.13, 11.59, 1, 1, 12, 21.31, 0.69, 1, 1, 12, 23.49, -10.21, 1, 2, 11, 32.02, 9.83, 0.49754, 12, 15.55, -13.39, 0.50246, 1, 11, 36.62, -5.77, 1, 1, 11, 27.88, -9.96, 1, 2, 10, 44.45, 12.8, 0.51478, 11, 19.12, -14.18, 0.48522, 1, 10, 52.26, -2.65, 1, 1, 10, 10.37, -33.52, 1], "hull": 15, "edges": [12, 10, 16, 18, 18, 20, 24, 26, 26, 28, 28, 0, 0, 2, 2, 4, 8, 10, 24, 4, 18, 8, 20, 22, 22, 24, 4, 6, 6, 8, 22, 6, 26, 2, 12, 14, 14, 16], "width": 98, "height": 76}}, "Layer 15": {"Layer 15": {"x": 17.47, "y": 7.67, "rotation": -172.23, "width": 95, "height": 78}}, "Layer 16": {"Layer 16": {"type": "mesh", "uvs": [1, 0.25488, 0.61502, 0.62981, 0.81976, 0.63508, 1, 0.75262, 1, 0.92463, 0.7884, 1, 0.35908, 1, 0, 0.93577, 0, 0.7843, 0.5, 0, 1, 0, 0.39697, 0.74978], "triangles": [5, 2, 3, 5, 1, 2, 4, 5, 3, 11, 1, 5, 6, 11, 5, 9, 10, 0, 1, 9, 0, 9, 1, 8, 1, 11, 8, 7, 8, 11, 6, 7, 11], "vertices": [1, 6, 44.82, -27.24, 1, 2, 6, 15.11, -13.54, 0.52064, 7, 8.95, 14.44, 0.47936, 2, 6, 16.38, -23.29, 0.08859, 7, 18.74, 15.28, 0.91141, 2, 6, 9.49, -33.24, 0.00278, 7, 28.38, 7.96, 0.99722, 1, 7, 29.93, -4.32, 1, 1, 7, 20.53, -10.97, 1, 2, 6, -13.22, -5.89, 0.22903, 7, 0.08, -13.54, 0.77097, 2, 6, -11.55, 11.88, 0.98509, 7, -17.6, -11.1, 0.01491, 1, 6, -0.8, 13.71, 1, 1, 6, 58.89, -0.5, 1, 1, 6, 62.91, -24.16, 1, 2, 6, 4.84, -4.66, 0.46041, 7, -0.36, 4.56, 0.53959], "hull": 11, "edges": [18, 20, 16, 18, 0, 20, 0, 2, 2, 4, 4, 6, 14, 16, 14, 12, 10, 12, 6, 8, 10, 8, 16, 22, 22, 2], "width": 48, "height": 72}}, "Layer 17": {"Layer 17": {"type": "mesh", "uvs": [0.50537, 0.55879, 0.68832, 0.50321, 0.93068, 0.55356, 1, 0.69137, 1, 1, 0.62437, 1, 0.26435, 0.95699, 0, 0.11147, 0, 0, 0.40546, 0], "triangles": [5, 0, 1, 3, 5, 1, 3, 1, 2, 6, 0, 5, 5, 3, 4, 7, 8, 9, 7, 9, 0, 6, 7, 0], "vertices": [2, 8, 5.54, -10.4, 0.47151, 9, -0.35, 10.88, 0.52849, 2, 8, 5.49, -21.99, 0.01475, 9, 9.94, 16.21, 0.98525, 1, 9, 24.99, 16.47, 1, 1, 9, 30.7, 9.78, 1, 1, 9, 34.24, -7.13, 1, 2, 8, -20.21, -10.63, 0.00049, 9, 11.81, -11.82, 0.99951, 2, 8, -11.89, 9.83, 0.63282, 9, -10.18, -13.96, 0.36718, 1, 8, 38.07, 12.4, 1, 1, 8, 44.07, 10.7, 1, 1, 8, 37.31, -13.1, 1], "hull": 10, "edges": [14, 16, 14, 12, 8, 10, 12, 10, 16, 18, 18, 0, 0, 2, 2, 4, 8, 6, 4, 6, 0, 12], "width": 61, "height": 56}}, "Layer 18": {"Layer 18": {"x": 38.69, "y": 6.26, "rotation": 169.77, "width": 96, "height": 78}}, "Layer 19": {"Layer 19": {"x": -4.71, "y": 2.31, "rotation": -90, "width": 51, "height": 70}}, "Layer 20": {"Layer 20": {"type": "mesh", "uvs": [0.90925, 0.14474, 1, 0.4465, 1, 0.81702, 0.79268, 1, 0.39634, 1, 0.11272, 0.89915, 0, 0.69365, 0, 0.54047, 0, 0.3873, 0.14274, 0.21922, 0.22222, 0.28416, 0.26637, 0.28034, 0.31229, 0.14474, 0.45535, 0, 0.72767, 0], "triangles": [1, 11, 0, 12, 0, 11, 12, 13, 14, 12, 14, 0, 10, 7, 8, 10, 8, 9, 10, 11, 7, 11, 6, 7, 11, 5, 6, 11, 1, 4, 1, 3, 4, 4, 5, 11, 2, 3, 1], "vertices": [1, 3, 12.17, -54.17, 1, 2, 2, 60.21, -87.22, 0.50286, 3, -39.73, -71.05, 0.49714, 1, 2, -3.52, -87.22, 1, 1, 2, -35, -48.66, 1, 1, 2, -35, 25.06, 1, 1, 2, -17.65, 77.81, 1, 2, 2, 17.7, 98.78, 0.4687, 22, -15.35, 45.97, 0.5313, 2, 2, 44.04, 98.78, 0.18864, 22, 7.33, 32.57, 0.81136, 1, 22, 30.01, 19.16, 1, 1, 22, 41.39, -18.4, 1, 1, 22, 24.25, -25.44, 1, 2, 2, 88.79, 49.23, 0.75615, 22, 20.64, -32.85, 0.24385, 1, 3, 12.17, 56.87, 1, 1, 3, 37.06, 30.26, 1, 1, 3, 37.06, -20.39, 1], "hull": 15, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 186, "height": 172}}, "Layer 22": {"Layer 22": {"type": "mesh", "uvs": [1, 0.26406, 0.94396, 0.46406, 0.8311, 0.69354, 0.68896, 0.87669, 0.49753, 1, 0.29304, 1, 0.0969, 0.78705, 0, 0.6109, 0, 0.22181, 0.12741, 0.45304, 0.28432, 0.63289, 0.48046, 0.64574, 0.67224, 0.54297, 0.85966, 0.28604, 0.92504, 0, 1, 0], "triangles": [14, 15, 0, 13, 14, 0, 1, 13, 0, 7, 8, 9, 2, 12, 13, 2, 13, 1, 6, 7, 9, 6, 9, 10, 3, 12, 2, 11, 12, 3, 5, 10, 11, 6, 10, 5, 4, 11, 3, 5, 11, 4], "vertices": [2, 26, 10.68, -30.4, 0.49143, 18, -7.24, -23.36, 0.50857, 2, 26, 6.88, -27.26, 0.62286, 18, -11.04, -20.22, 0.37714, 2, 26, 2.52, -20.94, 0.84571, 18, -15.4, -13.9, 0.15429, 1, 26, -0.96, -12.98, 1, 1, 26, -3.3, -2.26, 1, 1, 26, -3.3, 9.19, 1, 2, 26, 0.75, 20.17, 0.66286, 18, -17.17, 27.21, 0.33714, 2, 26, 4.09, 25.6, 0.47429, 18, -13.83, 32.64, 0.52571, 2, 26, 11.49, 25.6, 0.47429, 18, -6.43, 32.64, 0.52571, 2, 26, 7.09, 18.47, 0.66286, 18, -10.83, 25.5, 0.33714, 1, 26, 3.68, 9.68, 1, 1, 26, 3.43, -1.3, 1, 1, 26, 5.38, -12.04, 1, 2, 26, 10.27, -22.54, 0.62286, 18, -7.65, -15.5, 0.37714, 2, 26, 15.7, -26.2, 0.49143, 18, -2.22, -19.16, 0.50857, 2, 26, 15.7, -30.4, 0.49143, 18, -2.22, -23.36, 0.50857], "hull": 16, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 26, 28, 14, 16, 14, 12, 12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 0, 30, 2, 0], "width": 56, "height": 19}}, "Layer 23": {"Layer 23": {"type": "mesh", "uvs": [1, 0.5, 0.70765, 1, 0.26282, 1, 0, 0.57668, 0, 0.36502, 0, 0.15336, 0.16678, 0.28513, 0.35886, 0.37926, 0.61666, 0.3322, 0.81886, 0.1816, 1, 0, 0.187, 0.57692, 0.40436, 0.67105, 0.63688, 0.65222, 0.84919, 0.43573], "triangles": [2, 12, 1, 4, 5, 6, 14, 9, 10, 8, 9, 14, 14, 10, 0, 11, 3, 4, 11, 6, 7, 11, 4, 6, 13, 8, 14, 12, 7, 8, 12, 8, 13, 11, 7, 12, 2, 11, 12, 3, 11, 2, 1, 13, 14, 1, 14, 0, 12, 13, 1], "vertices": [2, 25, 11.04, -27.83, 0.71429, 18, -20.18, -23.22, 0.28571, 1, 18, -34.68, -7.44, 1, 1, 18, -34.68, 16.58, 1, 2, 25, 10.86, 26.22, 0.69143, 18, -22.41, 30.78, 0.30857, 1, 25, 16.99, 25.98, 1, 1, 25, 23.12, 25.75, 1, 1, 25, 18.96, 16.9, 1, 1, 25, 15.85, 6.64, 1, 1, 25, 16.68, -7.33, 1, 1, 25, 20.64, -18.4, 1, 1, 25, 25.53, -28.38, 1, 1, 25, 10.47, 16.13, 1, 1, 25, 7.3, 4.5, 1, 1, 25, 7.37, -8.07, 1, 1, 25, 13.21, -19.76, 1], "hull": 11, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20, 0, 2, 2, 4, 6, 4, 6, 8, 8, 10, 8, 22, 22, 24, 24, 26, 26, 28, 28, 20], "width": 54, "height": 29}}, "Layer 24": {"Layer 24": {"type": "mesh", "uvs": [1, 0.5, 0.78999, 1, 0.56421, 1, 0.28211, 1, 0, 0.75, 0, 0.5, 0.2347, 0.59498, 0.44217, 0.67634, 0.66795, 0.61126, 0.81439, 0.39972, 0.87542, 0, 1, 0], "triangles": [2, 8, 1, 1, 9, 0, 1, 8, 9, 3, 7, 2, 2, 7, 8, 4, 6, 3, 3, 6, 7, 4, 5, 6, 9, 10, 0, 10, 11, 0], "vertices": [2, 29, 5.26, -15.16, 0.50857, 20, -6.02, -20.13, 0.49143, 2, 29, -2.24, -6.76, 0.69143, 20, -13.52, -11.73, 0.30857, 1, 29, -2.24, 2.27, 1, 2, 29, -2.24, 13.55, 0.65714, 20, -13.52, 8.58, 0.34286, 2, 29, 1.51, 24.84, 0.53714, 20, -9.77, 19.87, 0.46286, 2, 29, 5.26, 24.84, 0.53714, 20, -6.02, 19.87, 0.46286, 2, 29, 3.83, 15.45, 0.65714, 20, -7.44, 10.48, 0.34286, 1, 29, 2.61, 7.15, 1, 1, 29, 3.59, -1.88, 1, 2, 29, 6.76, -7.74, 0.69143, 20, -4.51, -12.71, 0.30857, 2, 29, 12.76, -10.18, 0.50857, 20, 1.48, -15.15, 0.49143, 2, 29, 12.76, -15.16, 0.50857, 20, 1.48, -20.13, 0.49143], "hull": 12, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 18, 20, 0, 22, 0, 2, 2, 4, 4, 6, 8, 10, 6, 8], "width": 40, "height": 15}}, "Layer 25": {"Layer 25": {"type": "mesh", "uvs": [1, 0.18804, 1, 1, 0.35426, 1, 0, 0.30277, 0, 0.15139, 0, 0, 0.06698, 0, 0.17211, 0.131, 0.30185, 0.22495, 0.50988, 0.26522, 0.71121, 0.19811, 1, 0, 0.17435, 0.36588, 0.44054, 0.4766, 0.72016, 0.47325, 0.89687, 0.36588], "triangles": [1, 2, 14, 3, 12, 2, 2, 13, 14, 14, 15, 1, 15, 0, 1, 2, 12, 13, 12, 8, 13, 13, 9, 14, 13, 8, 9, 9, 10, 14, 14, 10, 15, 15, 10, 0, 12, 4, 7, 12, 7, 8, 12, 3, 4, 0, 10, 11, 4, 6, 7, 4, 5, 6], "vertices": [1, 28, 15.1, -14.41, 1, 1, 20, -27.85, -17.81, 1, 1, 20, -27.85, 5.43, 1, 2, 28, 15.5, 21.69, 0.52571, 20, -11.12, 18.19, 0.47429, 1, 28, 19.12, 21.38, 1, 1, 28, 22.74, 21.06, 1, 1, 28, 22.53, 18.66, 1, 1, 28, 19.07, 15.16, 1, 1, 28, 16.41, 10.7, 1, 1, 28, 14.79, 3.33, 1, 1, 28, 15.77, -4.03, 1, 1, 28, 19.59, -14.8, 1, 1, 28, 13.44, 15.57, 1, 1, 28, 9.96, 6.26, 1, 1, 28, 9.16, -3.78, 1, 1, 28, 11.17, -10.34, 1], "hull": 12, "edges": [2, 4, 6, 4, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 6, 8, 8, 10, 8, 24, 24, 26, 26, 28, 28, 30, 2, 0, 0, 22, 30, 0], "width": 36, "height": 24}}, "Layer 1212": {"Layer 1212": {"x": -1.56, "y": 1, "scaleX": 0.626, "scaleY": 0.626, "width": 77, "height": 65}}, "Layer 1213": {"Layer 1212": {"x": -1.56, "y": 1, "scaleX": 0.626, "scaleY": 0.626, "width": 77, "height": 65}}, "Layer 45555555": {"Layer 45555555": {"x": 1.06, "y": 0.32, "width": 38, "height": 38}}, "Layer 45555556": {"Layer 45555555": {"x": 1.06, "y": 0.32, "width": 38, "height": 38}}, "Layer 55555555": {"Layer 55555555": {"x": 0.74, "y": -0.94, "width": 17, "height": 58}}, "Layer 55555556": {"Layer 55555555": {"x": 0.74, "y": -0.94, "width": 17, "height": 58}}, "Layer 533333333": {"Layer 533333333": {"x": 0.15, "y": 1.01, "width": 9, "height": 8}}, "Layer 533333334": {"Layer 533333333": {"x": 0.15, "y": 1.01, "width": 9, "height": 8}}, "Layer 655555555": {"Layer 655555555": {"x": -1.05, "y": -0.87, "width": 37, "height": 23}}, "Layer 655555556": {"Layer 655555555": {"x": -1.05, "y": -0.87, "width": 37, "height": 23}}, "mouth-inside": {"mouth-inside": {"x": -5.72, "y": -11.8, "rotation": -90, "width": 30, "height": 32}}, "shadow": {"shadow": {"x": -0.49, "y": 24.55, "width": 182, "height": 143}}, "wing-down": {"wing-down": {"x": 39.18, "y": 6.17, "rotation": 169.77, "width": 89, "height": 70}}, "wing-up": {"wing-up": {"x": 16.84, "y": 8.59, "rotation": -172.23, "width": 88, "height": 72}}, "yazik": {"yazik": {"type": "mesh", "uvs": [1, 0.07762, 0.74177, 0.16362, 0.67118, 0.33362, 0.75353, 0.53762, 0.78177, 0.80162, 0.5, 1, 0.20059, 1, 0, 0.84762, 0, 0.5, 0.20765, 0.26562, 0.42648, 0.07962, 0.62648, 0, 1, 0], "triangles": [8, 9, 3, 7, 8, 3, 7, 3, 4, 5, 6, 7, 4, 5, 7, 1, 11, 12, 0, 1, 12, 10, 11, 1, 2, 10, 1, 9, 10, 2, 9, 2, 3], "vertices": [1, 16, -3.54, 5.64, 1, 2, 16, 5.36, 2.54, 0.9995, 17, -11.82, 13.94, 0.0005, 2, 16, 11.61, 6.14, 0.83928, 17, -4.95, 11.77, 0.16072, 2, 16, 14.79, 14.16, 0.29757, 17, 3.11, 14.85, 0.70243, 2, 16, 20.94, 22.8, 0.04581, 17, 13.63, 16.17, 0.95419, 1, 17, 21.89, 6.87, 1, 1, 17, 22.25, -3.3, 1, 1, 17, 16.39, -10.33, 1, 2, 16, 33.27, -3.66, 0.03101, 17, 2.49, -10.81, 0.96899, 2, 16, 21.8, -6.18, 0.91781, 17, -7.12, -4.07, 0.08219, 1, 16, 11.31, -6.99, 1, 1, 16, 4.08, -4.98, 1, 1, 16, -5.56, 3.28, 1], "hull": 13, "edges": [10, 8, 8, 6, 6, 4, 4, 2, 0, 24, 2, 0, 22, 24, 22, 20, 20, 18, 18, 16, 14, 16, 10, 12, 14, 12, 14, 8, 16, 6, 18, 4, 20, 2], "width": 34, "height": 40}}}}], "animations": {"death": {"slots": {"2211132333": {"attachment": [{"time": 0.1333, "name": "2211132333"}]}, "11112222211": {"attachment": [{"time": 0.1333, "name": "11112222211"}]}, "22231113344": {"attachment": [{"time": 0.1333, "name": "22231113344"}]}, "1111122222333": {"attachment": [{"time": 0.1333, "name": "1111122222333"}]}, "1111122223344": {"attachment": [{"time": 0.1333, "name": "1111122223344"}]}, "1111122323232": {"attachment": [{"time": 0.1333, "name": "1111122323232"}]}, "1112223333434": {"attachment": [{"time": 0.1333, "name": "1112223333434"}]}, "111122223333444": {"attachment": [{"time": 0.1333, "name": "111122223333444"}]}, "beak-down": {"attachment": [{"time": 0.1333}]}, "beak-up": {"attachment": [{"time": 0.1333}]}, "body": {"attachment": [{"time": 0.1333}]}, "crest": {"attachment": [{"time": 0.1333}]}, "Group 1000006385 6": {"rgba": [{"time": 0.7667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.2, "name": "Group 1000006385 6"}]}, "Group 1000006385 7": {"rgba": [{"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "Group 1000006385 6"}]}, "Group 1000006386": {"rgba": [{"time": 0.8667, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "Group 1000006386"}]}, "Group 1000006387": {"rgba": [{"time": 0.7667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"time": 0.1333, "name": "Group 1000006386"}]}, "Layer 3": {"attachment": [{"time": 0.1333}]}, "Layer 4": {"attachment": [{"time": 0.1333}]}, "Layer 5": {"attachment": [{"time": 0.1333}]}, "Layer 6": {"attachment": [{"time": 0.1333}]}, "Layer 7": {"attachment": [{"time": 0.1333}]}, "Layer 8": {"attachment": [{"time": 0.1333}]}, "Layer 9": {"attachment": [{"time": 0.1333}]}, "Layer 10": {"attachment": [{"time": 0.1333}]}, "Layer 11": {"attachment": [{"time": 0.1333}]}, "Layer 12": {"attachment": [{"time": 0.1333}]}, "Layer 13": {"attachment": [{"time": 0.1333}]}, "Layer 14": {"attachment": [{"time": 0.1333}]}, "Layer 15": {"attachment": [{"time": 0.1333}]}, "Layer 16": {"attachment": [{"time": 0.1333}]}, "Layer 17": {"attachment": [{"time": 0.1333}]}, "Layer 18": {"attachment": [{"time": 0.1333}]}, "Layer 19": {"attachment": [{"time": 0.1333}]}, "Layer 20": {"attachment": [{"time": 0.1333}]}, "mouth-inside": {"attachment": [{"time": 0.1333}]}, "shadow": {"attachment": [{"time": 0.2667}]}, "wing-down": {"attachment": [{"time": 0.1333}]}, "wing-up": {"attachment": [{"time": 0.1333}]}, "yazik": {"attachment": [{"time": 0.1333}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.1333, "value": 14.63}], "translate": [{}, {"time": 0.1333, "x": 11.09, "y": -12.33}], "scale": [{}, {"time": 0.1333, "x": 1.284, "y": 1.025}]}, "bone4": {"translate": [{}, {"time": 0.1333, "x": 57.98, "y": 25.8}]}, "bone5": {"translate": [{}, {"time": 0.1333, "x": 4.69, "y": -17.4}]}, "bone6": {"translate": [{}, {"time": 0.1333, "x": -39.68, "y": -32.07}]}, "bone7": {"rotate": [{}, {"time": 0.1333, "value": 8.18}]}, "bone8": {"translate": [{}, {"time": 0.1333, "x": 36.63, "y": -33.72}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "value": 39.83}]}, "bone10": {"translate": [{}, {"time": 0.1333, "x": -48.1, "y": -18.42}]}, "bone13": {"translate": [{}, {"time": 0.1333, "x": 3.69, "y": 13}]}, "bone18": {"translate": [{}, {"time": 0.1333, "x": 27.12, "y": 27.12}]}, "bone20": {"translate": [{}, {"time": 0.1333, "x": 33.97, "y": 1.18}]}, "bone39": {"rotate": [{"time": 0.2, "value": 12.52}, {"time": 0.4, "value": 79.26}, {"time": 0.6, "value": 148.34}, {"time": 1.3333, "value": 291.22}], "translate": [{"time": 0.2, "x": -465.33, "y": 25.89}, {"time": 0.4, "x": -501.69, "y": 239.75}, {"time": 0.6, "x": -636.05, "y": 260.71}, {"time": 1.3333, "x": -719.87, "y": -44.38}], "scale": [{"time": 0.2, "x": 0.684, "y": 0.684}, {"time": 0.4, "x": 0.876, "y": 0.876}]}, "bone40": {"rotate": [{"time": 0.2333}, {"time": 0.5, "value": -84.1}, {"time": 0.7667, "value": -145.55}, {"time": 1.6, "value": -289.64}], "translate": [{"time": 0.2333, "x": -466.56, "y": 42.53}, {"time": 0.5, "x": -531.27, "y": 307.55}, {"time": 0.7667, "x": -429.58, "y": 348.84}, {"time": 1.6, "x": -207.7, "y": -1.23}]}, "bone31": {"translate": [{"x": -5.55, "y": 371.03, "curve": "stepped"}, {"time": 0.1333, "x": -5.55, "y": 371.03}, {"time": 0.2667, "x": -6.21, "y": 343.23}], "scale": [{"time": 0.1333, "x": 0.952}, {"time": 0.2667, "x": 0.94, "y": 1.092}, {"time": 0.4}]}, "bone32": {"translate": [{"time": 0.4}, {"time": 1.0333, "x": 2.89, "y": 0.76}, {"time": 1.6667}]}, "bone33": {"rotate": [{"time": 0.4667}, {"time": 1.0667, "value": -9.63}, {"time": 1.6667}], "translate": [{"time": 0.1333, "x": -21.31, "y": 24.11}, {"time": 0.3}], "scale": [{"time": 0.3, "x": 1.284, "y": 1.284}, {"time": 0.4667}]}, "bone34": {"rotate": [{"time": 0.4}, {"time": 0.9667, "value": 7.12}, {"time": 1.5333}], "translate": [{"time": 0.1333, "x": -1.91, "y": -12.19}, {"time": 0.2667}], "scale": [{"time": 0.2667, "x": 1.26, "y": 1.26}, {"time": 0.4}]}, "bone35": {"rotate": [{"time": 0.1333, "value": 14.51}, {"time": 0.2667, "value": 40.71}, {"time": 0.4}, {"time": 0.9667, "value": 24.06}, {"time": 1.5333}], "translate": [{"time": 0.1333, "x": 4.68, "y": 10.36}, {"time": 0.2667, "x": 14.85, "y": -1.39}, {"time": 0.4}], "scale": [{"time": 0.1333, "x": 1.715, "y": 1.715}, {"time": 0.2667, "x": 0.994, "y": 0.994}, {"time": 0.4}]}, "bone36": {"rotate": [{"time": 0.1333, "value": -21.62}, {"time": 0.3333, "value": -29.5}, {"time": 0.5333}, {"time": 1.1, "value": -16.38}, {"time": 1.6667}], "translate": [{"time": 0.1333, "x": -3.32, "y": -6.86}, {"time": 0.3333, "x": 2.76, "y": -1.84}, {"time": 0.5333}], "scale": [{"time": 0.1333, "x": 1.602, "y": 1.602}, {"time": 0.3333}]}, "bone37": {"rotate": [{"time": 0.1333, "value": -54.82}, {"time": 0.3333, "value": -28.4}, {"time": 0.5}, {"time": 1.0667, "value": -7.04}, {"time": 1.6667}], "translate": [{"time": 0.1333, "x": -38.81, "y": 25.99}, {"time": 0.3333, "x": 15.86, "y": -11.49}, {"time": 0.5}, {"time": 1.0667, "x": 16.97, "y": -6.95}, {"time": 1.6667}]}, "bone38": {"rotate": [{"time": 0.1333, "value": 45.09}, {"time": 0.2667, "value": 23.11}, {"time": 0.4}, {"time": 0.9667, "value": -7.37}, {"time": 1.5333}], "translate": [{"time": 0.1333, "x": -17.37, "y": -16.29}, {"time": 0.2667, "x": 20.57, "y": 17.11}, {"time": 0.4}, {"time": 0.9667, "x": 17.66, "y": 12.69}, {"time": 1.5333}]}, "bone30": {"translate": [{"x": 15.66, "y": 86.86}]}, "bone41": {"rotate": [{"time": 0.1333}, {"time": 0.4, "value": -84.1}, {"time": 0.6667, "value": -145.55}, {"time": 1.5, "value": -289.64}], "translate": [{"time": 0.1333, "x": -466.56, "y": 42.53}, {"time": 0.4, "x": -531.27, "y": 307.55}, {"time": 0.6667, "x": -586.74, "y": 250.84}, {"time": 1.5, "x": -644.06, "y": 1.85}]}, "bone42": {"rotate": [{"time": 0.4667, "value": 12.52}, {"time": 0.6667, "value": 79.26}, {"time": 0.8667, "value": 148.34}, {"time": 1.6, "value": 291.22}], "translate": [{"time": 0.4667, "x": -465.33, "y": 25.89}, {"time": 0.6667, "x": -447.45, "y": 244.68}, {"time": 0.8667, "x": -374.11, "y": 201.54}, {"time": 1.6, "x": -393.83, "y": -11.71}], "scale": [{"time": 0.4667, "x": 0.684, "y": 0.684}, {"time": 0.6667, "x": 0.876, "y": 0.876}]}}}, "finish_back": {"slots": {"778887899898": {"attachment": [{"name": "778887899898"}]}, "988998675654": {"attachment": [{"name": "988998675654"}]}, "beak-down": {"attachment": [{}]}, "beak-up": {"attachment": [{}]}, "body": {"attachment": [{}]}, "crest": {"attachment": [{}]}, "Layer 3": {"attachment": [{}]}, "Layer 4": {"attachment": [{}]}, "Layer 5": {"attachment": [{}]}, "Layer 6": {"attachment": [{}]}, "Layer 7": {"attachment": [{}]}, "Layer 8": {"attachment": [{}]}, "Layer 9": {"attachment": [{}]}, "Layer 10": {"attachment": [{}]}, "Layer 11": {"attachment": [{}]}, "Layer 12": {"attachment": [{}]}, "Layer 13": {"attachment": [{}]}, "Layer 14": {"attachment": [{}]}, "Layer 15": {"attachment": [{}]}, "Layer 16": {"attachment": [{}]}, "Layer 17": {"attachment": [{}]}, "Layer 18": {"attachment": [{}]}, "Layer 19": {"attachment": [{}]}, "Layer 20": {"attachment": [{}]}, "mouth-inside": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "wing-down": {"attachment": [{}]}, "wing-up": {"attachment": [{}]}, "yazik": {"attachment": [{}]}}, "bones": {"bone55": {"translate": [{"x": -169.02, "y": 88.45}]}, "bone56": {"translate": [{"time": 1.6667}]}, "bone58": {"rotate": [{}, {"time": 0.1667, "value": 10.29}, {"time": 0.4, "value": 36.11}, {"time": 0.6667, "value": -15.04}, {"time": 0.9667, "value": 1.88}]}, "bone59": {"rotate": [{}, {"time": 0.2, "value": 10.29}, {"time": 0.4333, "value": 36.11}, {"time": 0.7, "value": -15.04}, {"time": 1, "value": 1.88}]}, "bone60": {"rotate": [{}, {"time": 0.2333, "value": 10.29}, {"time": 0.4667, "value": 36.11}, {"time": 0.7333, "value": -15.04}, {"time": 1.0333, "value": 1.88}]}, "bone62": {"rotate": [{}, {"time": 0.1667, "value": -8.71}, {"time": 0.4, "value": -46.32}, {"time": 0.6667, "value": 32.56}, {"time": 0.9667, "value": 25.9}]}, "bone63": {"rotate": [{}, {"time": 0.1667, "value": -8.71}, {"time": 0.4, "value": -26.66}, {"time": 0.6667, "value": 40.82}, {"time": 0.9667, "value": 12.93}]}}}, "finish_front": {"slots": {"566778878990": {"attachment": [{"name": "566778878990"}]}, "3344555656556": {"attachment": [{"name": "3344555656556"}]}, "123122331112334": {"rgba": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": "123122331112334"}]}, "beak-down": {"attachment": [{}]}, "beak-up": {"attachment": [{}]}, "body": {"attachment": [{}]}, "crest": {"attachment": [{}]}, "Layer 3": {"attachment": [{}]}, "Layer 4": {"attachment": [{}]}, "Layer 5": {"attachment": [{}]}, "Layer 6": {"attachment": [{}]}, "Layer 7": {"attachment": [{}]}, "Layer 8": {"attachment": [{}]}, "Layer 9": {"attachment": [{}]}, "Layer 10": {"attachment": [{}]}, "Layer 11": {"attachment": [{}]}, "Layer 12": {"attachment": [{}]}, "Layer 13": {"attachment": [{}]}, "Layer 14": {"attachment": [{}]}, "Layer 15": {"attachment": [{}]}, "Layer 16": {"attachment": [{}]}, "Layer 17": {"attachment": [{}]}, "Layer 18": {"attachment": [{}]}, "Layer 19": {"attachment": [{}]}, "Layer 20": {"attachment": [{}]}, "Layer 1212": {"rgba": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Layer 1212"}]}, "Layer 1213": {"rgba": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00"}], "attachment": [{"name": "Layer 1212"}]}, "Layer 45555555": {"rgba": [{"time": 0.5, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"name": "Layer 45555555"}]}, "Layer 45555556": {"rgba": [{"time": 0.5, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"name": "Layer 45555555"}]}, "Layer 55555555": {"rgba": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"name": "Layer 55555555"}]}, "Layer 55555556": {"rgba": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"name": "Layer 55555555"}]}, "Layer 533333333": {"rgba": [{"time": 0.7667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": "Layer 533333333"}]}, "Layer 533333334": {"rgba": [{"time": 0.7667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": "Layer 533333333"}]}, "Layer 655555555": {"rgba": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": "Layer 655555555"}]}, "Layer 655555556": {"rgba": [{"time": 0.5667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"name": "Layer 655555555"}]}, "mouth-inside": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "wing-down": {"attachment": [{}]}, "wing-up": {"attachment": [{}]}, "yazik": {"attachment": [{}]}}, "bones": {"bone55": {"translate": [{"x": -169.02, "y": 88.45}]}, "bone56": {"translate": [{"time": 1.6667}]}, "bone58": {"rotate": [{}, {"time": 0.1667, "value": 10.29}, {"time": 0.4, "value": 36.11}, {"time": 0.6667, "value": -15.04}, {"time": 0.9667, "value": 1.88}]}, "bone59": {"rotate": [{}, {"time": 0.2, "value": 10.29}, {"time": 0.4333, "value": 36.11}, {"time": 0.7, "value": -15.04}, {"time": 1, "value": 1.88}]}, "bone60": {"rotate": [{}, {"time": 0.2333, "value": 10.29}, {"time": 0.4667, "value": 36.11}, {"time": 0.7333, "value": -15.04}, {"time": 1.0333, "value": 1.88}]}, "bone61": {"rotate": [{}, {"time": 0.1667, "value": -8.71}, {"time": 0.4, "value": -55.3}, {"time": 0.6667, "value": -178.82}, {"time": 0.9667, "value": -165.71}]}, "bone62": {"rotate": [{}, {"time": 0.1667, "value": -8.71}, {"time": 0.4, "value": -46.32}, {"time": 0.6667, "value": 32.56}, {"time": 0.9667, "value": 25.9}]}, "bone63": {"rotate": [{}, {"time": 0.1667, "value": -8.71}, {"time": 0.4, "value": -26.66}, {"time": 0.6667, "value": 40.82}, {"time": 0.9667, "value": 12.93}]}, "bone43": {"translate": [{"x": 2.2, "y": -183.09}]}, "bone44": {"rotate": [{}, {"time": 0.2333, "value": -38.68, "curve": [0.467, -41.44, 0.7, -41.04]}, {"time": 0.9333, "value": -41.04}], "translate": [{}, {"time": 0.2333, "y": 142.99, "curve": [0.467, 0, 0.7, -0.55, 0.467, 142.99, 0.7, -61.07]}, {"time": 0.9333, "x": -0.55, "y": -61.07}], "scale": [{"x": 0.358, "y": 0.358}, {"time": 0.2333, "x": 2.343, "y": 2.343, "curve": [0.467, 2.736, 0.7, 2.759, 0.467, 2.736, 0.7, 2.759]}, {"time": 0.9333, "x": 2.759, "y": 2.759}]}, "bone45": {"rotate": [{}, {"time": 0.2667, "value": 237.79}, {"time": 0.9667, "value": 539.53}], "translate": [{}, {"time": 0.1333, "x": -13.5, "y": 110.25}, {"time": 0.2667, "x": -60.12, "y": 133.48}, {"time": 0.5, "x": -121.77, "y": 98.81}, {"time": 0.9667, "x": -200.73, "y": -126.86}], "scale": [{"x": 0.249, "y": 0.249}, {"time": 0.2667}]}, "bone46": {"rotate": [{}, {"time": 0.3, "value": -151.76}, {"time": 0.9333, "value": -499.21}], "translate": [{"x": 2.13, "y": 4.25}, {"time": 0.3, "x": 29.78, "y": 239.28}, {"time": 0.9333, "x": 34.03, "y": -47.86}], "scale": [{"x": 0.458, "y": 0.458}, {"time": 0.3}]}, "bone47": {"rotate": [{}, {"time": 0.2667, "value": -165.47}, {"time": 0.9667, "value": -503.45}], "translate": [{"x": -8.51, "y": 22.33}, {"time": 0.2667, "x": 67, "y": 199.93}, {"time": 0.5667, "x": 104.37, "y": 127.16}, {"time": 0.9667, "x": 102.09, "y": -61.68}], "scale": [{"x": 0.339, "y": 0.339}, {"time": 0.2667}]}, "bone48": {"translate": [{"x": 18.44, "y": -23.23}, {"time": 0.2333, "x": 20.74, "y": 113.33}, {"time": 0.3667, "x": 2.73, "y": 151.65}, {"time": 0.5667, "x": -37.23, "y": 69.68}, {"time": 0.9333, "x": -52.6, "y": -129.79}]}, "bone49": {"translate": [{"x": 18.44, "y": -23.23}, {"time": 0.2333, "x": 20.74, "y": 113.33}, {"time": 0.3667, "x": 2.73, "y": 151.65}, {"time": 0.5667, "x": -37.23, "y": 69.68}, {"time": 0.9333, "x": -52.6, "y": -129.79}]}, "bone50": {"rotate": [{"value": 82.17}, {"time": 0.2667, "value": -95.94}, {"time": 0.9667, "value": -356.87}], "translate": [{"x": -89.8, "y": 19.6}, {"time": 0.2667, "x": -95.58, "y": 201.98}, {"time": 0.5667, "x": -143.95, "y": 110.2}, {"time": 0.9667, "x": -154.75, "y": -129.99}], "scale": [{"x": 0.339, "y": 0.339}, {"time": 0.2667}]}, "bone51": {"rotate": [{}, {"time": 0.3, "value": -151.76}, {"time": 0.9333, "value": -499.21}], "translate": [{"x": -19.73, "y": -23.75}, {"time": 0.3, "x": -54.93, "y": 99.93}, {"time": 0.9333, "x": -114.2, "y": -148.27}], "scale": [{"x": 0.458, "y": 0.458}, {"time": 0.3}]}, "bone52": {"rotate": [{}, {"time": 0.2667, "value": 237.79}, {"time": 0.9667, "value": 539.53}], "translate": [{}, {"time": 0.1333, "x": 4.26, "y": 57.65}, {"time": 0.2667, "x": 47.03, "y": 101.72}, {"time": 0.5, "x": 83.84, "y": 48.95}, {"time": 0.9667, "x": 92.31, "y": -179.46}], "scale": [{"x": 0.249, "y": 0.249}, {"time": 0.2667}]}, "bone54": {"rotate": [{}, {"time": 0.3333, "value": -207.26, "curve": "stepped"}, {"time": 0.3667}, {"time": 0.7333, "value": -207.26}], "translate": [{"x": 11.15, "y": 89.51, "curve": "stepped"}, {"time": 0.3667, "x": -159.47, "y": 115.73}], "scale": [{"x": 0.354, "y": 0.354}, {"time": 0.1333}, {"time": 0.3333, "x": 0.262, "y": 0.262, "curve": "stepped"}, {"time": 0.3667, "x": 0.354, "y": 0.354}, {"time": 0.5333}, {"time": 0.7333, "x": 0.262, "y": 0.262}]}, "bone53": {"rotate": [{"time": 0.1667}, {"time": 0.5, "value": -207.26, "curve": "stepped"}, {"time": 0.5667}, {"time": 0.9333, "value": -207.26}], "translate": [{"time": 0.1667, "x": -82.09, "y": 148.26, "curve": "stepped"}, {"time": 0.5667, "x": -227.17, "y": 109.34}], "scale": [{"time": 0.1667, "x": 0.354, "y": 0.354}, {"time": 0.3}, {"time": 0.5, "x": 0.262, "y": 0.262, "curve": "stepped"}, {"time": 0.5667, "x": 0.354, "y": 0.354}, {"time": 0.7333}, {"time": 0.9333, "x": 0.262, "y": 0.262}]}}}, "idle": {"slots": {"Layer 5 copy": {"attachment": [{"time": 1.6, "name": "Layer 5 copy"}, {"time": 1.8}]}, "Layer 6 copy": {"attachment": [{"time": 1.5667, "name": "Layer 6 copy"}, {"time": 1.7667}]}, "Layer 22": {"attachment": [{"time": 1.5667, "name": "Layer 22"}, {"time": 1.7667}]}, "Layer 23": {"attachment": [{"time": 1.5667, "name": "Layer 23"}, {"time": 1.7667}]}, "Layer 24": {"attachment": [{"time": 1.6, "name": "Layer 24"}, {"time": 1.8}]}, "Layer 25": {"attachment": [{"time": 1.6, "name": "Layer 25"}, {"time": 1.8}]}}, "bones": {"bone2": {"rotate": [{"curve": [0.167, 0, 0.333, 1.39]}, {"time": 0.5, "value": 1.39, "curve": [0.667, 1.39, 0.833, 0]}, {"time": 1, "curve": [1.167, 0, 1.333, 1.39]}, {"time": 1.5, "value": 1.39, "curve": [1.667, 1.39, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.333, 1.39]}, {"time": 2.5, "value": 1.39, "curve": [2.667, 1.39, 2.833, 0]}, {"time": 3}], "scale": [{"curve": [0.167, 1, 0.333, 1.017, 0.167, 1, 0.333, 1.022]}, {"time": 0.5, "x": 1.017, "y": 1.022, "curve": [0.667, 1.017, 0.833, 1, 0.667, 1.022, 0.833, 1]}, {"time": 1, "curve": [1.167, 1, 1.333, 1.017, 1.167, 1, 1.333, 1.022]}, {"time": 1.5, "x": 1.017, "y": 1.022, "curve": [1.667, 1.017, 1.833, 1, 1.667, 1.022, 1.833, 1]}, {"time": 2, "curve": [2.167, 1, 2.333, 1.017, 2.167, 1, 2.333, 1.022]}, {"time": 2.5, "x": 1.017, "y": 1.022, "curve": [2.667, 1.017, 2.833, 1, 2.667, 1.022, 2.833, 1]}, {"time": 3}]}, "bone3": {"translate": [{"x": 0.29, "curve": [0.034, 0.12, 0.067, 0, 0.034, 0, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 2.75, 0.267, 0, 0.433, 0]}, {"time": 0.6, "x": 2.75, "curve": [0.734, 2.75, 0.867, 0.95, 0.734, 0, 0.867, 0]}, {"time": 1, "x": 0.29, "curve": [1.034, 0.12, 1.067, 0, 1.034, 0, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, 2.75, 1.267, 0, 1.433, 0]}, {"time": 1.6, "x": 2.75, "curve": [1.633, 2.75, 1.667, 2.63, 1.633, 0, 1.667, 0]}, {"time": 1.7, "x": 2.45, "curve": [1.722, 2.33, 1.745, 2.11, 1.722, 0, 1.745, -6.66]}, {"time": 1.7667, "x": 1.95, "y": -6.66, "curve": "stepped"}, {"time": 2.1667, "x": 1.95, "y": -6.66, "curve": [2.177, 1.87, 2.211, 0, 2.177, -6.66, 2.211, 0]}, {"time": 2.2333, "curve": [2.252, 0, 2.478, 2.75, 2.252, 0, 2.478, 0]}, {"time": 2.6, "x": 2.75, "curve": [2.734, 2.75, 2.868, 0.99, 2.734, 0, 2.868, 0]}, {"time": 3, "x": 0.29}]}, "bone4": {"translate": [{"curve": [0.167, 0, 0.333, 4.96, 0.167, 0, 0.333, 4.38]}, {"time": 0.5, "x": 4.96, "y": 4.38, "curve": [0.667, 4.96, 0.833, 0, 0.667, 4.38, 0.833, 0]}, {"time": 1, "curve": [1.167, 0, 1.333, 4.96, 1.167, 0, 1.333, 4.38]}, {"time": 1.5, "x": 4.96, "y": 4.38, "curve": [1.667, 4.96, 1.833, 0, 1.667, 4.38, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.333, 4.96, 2.167, 0, 2.333, 4.38]}, {"time": 2.5, "x": 4.96, "y": 4.38, "curve": [2.667, 4.96, 2.833, 0, 2.667, 4.38, 2.833, 0]}, {"time": 3}]}, "bone5": {"translate": [{"curve": [0.167, 0, 0.333, 4.41, 0.167, 0, 0.333, -3.29]}, {"time": 0.5, "x": 4.41, "y": -3.29, "curve": [0.667, 4.41, 0.833, 0, 0.667, -3.29, 0.833, 0]}, {"time": 1, "curve": [1.167, 0, 1.333, 4.41, 1.167, 0, 1.333, -3.29]}, {"time": 1.5, "x": 4.41, "y": -3.29, "curve": [1.667, 4.41, 1.833, 0, 1.667, -3.29, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.333, 4.41, 2.167, 0, 2.333, -3.29]}, {"time": 2.5, "x": 4.41, "y": -3.29, "curve": [2.667, 4.41, 2.833, 0, 2.667, -3.29, 2.833, 0]}, {"time": 3}]}, "bone10": {"rotate": [{"value": 0.16, "curve": [0.034, 0.07, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 1.58]}, {"time": 0.6, "value": 1.58, "curve": [0.734, 1.58, 0.867, 0.54]}, {"time": 1, "value": 0.16, "curve": [1.034, 0.07, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, 1.58]}, {"time": 1.6, "value": 1.58, "curve": [1.638, 1.58, 1.685, 1.49]}, {"time": 1.7333, "value": 1.36, "curve": [1.755, 1.31, 1.778, -2.97]}, {"time": 1.8, "value": -3.04, "curve": [1.822, -3.11, 1.845, 6.12]}, {"time": 1.8667, "value": 6.05, "curve": [1.913, 5.89, 1.958, 0.64]}, {"time": 2, "value": 0.49, "curve": [2.037, 0.36, 2.071, 0.25]}, {"time": 2.1, "value": 0.16, "curve": "stepped"}, {"time": 2.1667, "value": 0.16, "curve": [2.201, 0.07, 2.067, -7.65]}, {"time": 2.2333, "value": -7.65}, {"time": 2.3333, "value": 1.58, "curve": "stepped"}, {"time": 2.6, "value": 1.58, "curve": [2.734, 1.58, 2.868, 0.56]}, {"time": 3, "value": 0.16}], "translate": [{"x": -0.78, "y": 0.67, "curve": [0.034, -0.32, 0.067, 0, 0.034, 0.27, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, -7.54, 0.267, 0, 0.433, 6.42]}, {"time": 0.6, "x": -7.54, "y": 6.42, "curve": [0.734, -7.54, 0.867, -2.6, 0.734, 6.42, 0.867, 2.21]}, {"time": 1, "x": -0.78, "y": 0.67, "curve": [1.034, -0.32, 1.067, 0, 1.034, 0.27, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, -7.54, 1.267, 0, 1.433, 6.42]}, {"time": 1.6, "x": -7.54, "y": 6.42, "curve": [1.637, -7.54, 1.697, -7.27, 1.637, 6.42, 1.697, 6.19]}, {"time": 1.7333, "x": -6.72, "y": 5.72, "curve": [1.756, -6.39, 1.778, -7.58, 1.756, 5.44, 1.778, 18.52]}, {"time": 1.8, "x": -7.12, "y": 18.13}, {"time": 1.8667, "x": -6.58, "y": 17.36, "curve": "stepped"}, {"time": 2.1667, "x": -6.58, "y": 17.36}, {"time": 2.2333, "x": -7.54, "y": 6.42, "curve": "stepped"}, {"time": 2.6, "x": -7.54, "y": 6.42, "curve": [2.734, -7.54, 2.868, -2.7, 2.734, 6.42, 2.868, 2.3]}, {"time": 3, "x": -0.78, "y": 0.67}]}, "bone11": {"rotate": [{"time": 0.1, "curve": [0.267, 0, 0.433, 5.63]}, {"time": 0.6, "value": 5.63}, {"time": 1.1, "curve": [1.267, 0, 1.433, 5.96]}, {"time": 1.6, "value": 5.96}, {"time": 1.7667}, {"time": 1.8333, "value": -7.21}, {"time": 1.9, "value": 9.72}, {"time": 2.0333, "curve": "stepped"}, {"time": 2.2, "curve": [2.278, 0, 2.19, -9.22]}, {"time": 2.2667, "value": -9.22, "curve": [2.301, -9.22, 2.334, 0]}, {"time": 2.3667}], "translate": [{"time": 1.7667}, {"time": 1.8333, "x": -2.98, "y": 3.85}, {"time": 1.9, "x": -1.07, "y": 0.17, "curve": "stepped"}, {"time": 2.2, "x": -1.07, "y": 0.17}, {"time": 2.3667, "x": -0.54, "y": 0.08}, {"time": 2.5333}]}, "bone12": {"rotate": [{"value": 6.98, "curve": [0.056, 4.88, 0.112, 2.66]}, {"time": 0.1667, "value": 1.32, "curve": [0.201, 0.53, 0.234, 0]}, {"time": 0.2667, "curve": [0.433, 0, 0.6, 12.68]}, {"time": 0.7667, "value": 12.68, "curve": [0.901, 12.68, 1.033, 4.37]}, {"time": 1.1667, "value": 1.32, "curve": [1.201, 0.53, 1.234, 0]}, {"time": 1.2667, "curve": [1.425, 0, 1.508, 12.68]}, {"time": 1.6667, "value": 12.68, "curve": "stepped"}, {"time": 1.8, "value": 12.68}, {"time": 1.8667, "value": -2.74}, {"time": 1.9333, "value": 22.07}, {"time": 2.0667, "value": 12.68, "curve": "stepped"}, {"time": 2.2333, "value": 12.68}, {"time": 2.3, "value": 3.46}, {"time": 2.4, "value": 12.68, "curve": "stepped"}, {"time": 2.7667, "value": 12.68, "curve": [2.845, 12.68, 2.923, 9.93]}, {"time": 3, "value": 6.98}], "translate": [{"x": -1.82, "y": 1.28, "curve": [0.056, -1.27, 0.112, -0.69, 0.056, 0.9, 0.112, 0.49]}, {"time": 0.1667, "x": -0.34, "y": 0.24, "curve": [0.201, -0.14, 0.234, 0, 0.201, 0.1, 0.234, 0]}, {"time": 0.2667, "curve": [0.433, 0, 0.6, -3.3, 0.433, 0, 0.6, 2.33]}, {"time": 0.7667, "x": -3.3, "y": 2.33, "curve": [0.901, -3.3, 1.033, -1.14, 0.901, 2.33, 1.033, 0.8]}, {"time": 1.1667, "x": -0.34, "y": 0.24, "curve": [1.201, -0.14, 1.234, 0, 1.201, 0.1, 1.234, 0]}, {"time": 1.2667, "curve": [1.425, 0, 1.508, -3.3, 1.425, 0, 1.508, 2.33]}, {"time": 1.6667, "x": -3.3, "y": 2.33, "curve": [1.704, -3.3, 1.763, -3.18, 1.704, 2.33, 1.763, 2.25]}, {"time": 1.8, "x": -2.94, "y": 2.08, "curve": [1.822, -2.79, 1.845, -12.82, 1.822, 1.98, 1.845, 7.41]}, {"time": 1.8667, "x": -12.62, "y": 7.27}, {"time": 1.9333, "x": -10.68, "y": 4.65, "curve": "stepped"}, {"time": 2.2333, "x": -10.68, "y": 4.65}, {"time": 2.3, "x": -3.3, "y": 2.33, "curve": "stepped"}, {"time": 2.7667, "x": -3.3, "y": 2.33, "curve": [2.845, -3.3, 2.923, -2.58, 2.845, 2.33, 2.923, 1.83]}, {"time": 3, "x": -1.82, "y": 1.28}]}, "bone13": {"rotate": [{"value": 0.47, "curve": [0.034, 0.19, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 4.53]}, {"time": 0.6, "value": 4.53, "curve": [0.734, 4.53, 0.867, 1.56]}, {"time": 1, "value": 0.47, "curve": [1.034, 0.19, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, 4.53]}, {"time": 1.6, "value": 4.53, "curve": [1.734, 4.53, 1.867, 1.56]}, {"time": 2, "value": 0.47, "curve": [2.034, 0.19, 2.067, 0]}, {"time": 2.1, "curve": [2.267, 0, 2.433, 4.53]}, {"time": 2.6, "value": 4.53, "curve": [2.734, 4.53, 2.868, 1.62]}, {"time": 3, "value": 0.47}], "translate": [{"x": 0.35, "curve": [0.034, 0.14, 0.067, 0, 0.034, 0, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 3.36, 0.267, 0, 0.433, 0]}, {"time": 0.6, "x": 3.36, "curve": [0.734, 3.36, 0.867, 1.16, 0.734, 0, 0.867, 0]}, {"time": 1, "x": 0.35, "curve": [1.034, 0.14, 1.067, 0, 1.034, 0, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, 3.36, 1.267, 0, 1.433, 0]}, {"time": 1.6, "x": 3.36, "curve": [1.633, 3.36, 1.667, 3.21, 1.633, 0, 1.667, 0]}, {"time": 1.7, "x": 2.99, "curve": [1.722, 2.85, 1.745, 2.82, 1.722, 0, 1.745, -2.68]}, {"time": 1.7667, "x": 2.62, "y": -2.68, "curve": "stepped"}, {"time": 2.1667, "x": 2.62, "y": -2.68, "curve": [2.177, 2.53, 2.211, 0, 2.177, -2.68, 2.211, 0]}, {"time": 2.2333, "curve": [2.252, 0, 2.478, 3.36, 2.252, 0, 2.478, 0]}, {"time": 2.6, "x": 3.36, "curve": [2.734, 3.36, 2.868, 1.2, 2.734, 0, 2.868, 0]}, {"time": 3, "x": 0.35}]}, "bone15": {"rotate": [{"curve": [0.167, 0, 0.333, -13.23]}, {"time": 0.5, "value": -13.23}, {"time": 1, "curve": [1.167, 0, 1.333, -10.94]}, {"time": 1.5, "value": -10.94}, {"time": 2, "curve": [2.167, 0, 2.333, -9.56]}, {"time": 2.5, "value": -9.56, "curve": [2.667, -9.56, 2.833, 0]}, {"time": 3}]}, "bone17": {"rotate": [{"value": 8.91, "curve": [0.119, 4.87, 0.249, 0]}, {"time": 0.3, "curve": [0.467, 0, 0.723, 14.32]}, {"time": 0.8, "value": 14.32, "curve": [0.838, 14.32, 0.933, 11.18]}, {"time": 1, "value": 8.91, "curve": [1.119, 4.87, 1.249, 0]}, {"time": 1.3, "curve": [1.411, 0, 1.564, 7.8]}, {"time": 1.6667, "value": 11.6, "curve": [1.71, 13.18, 1.745, -9.16]}, {"time": 1.7667, "value": -9.16, "curve": [1.794, -9.16, 1.845, 34.45]}, {"time": 1.9, "value": 32.8, "curve": [1.934, 31.79, 1.97, -3.48]}, {"time": 2, "value": -4.5, "curve": [2.033, -5.63, 2.068, 6.6]}, {"time": 2.1, "value": 5.47, "curve": [2.123, 4.68, 2.146, 3.93]}, {"time": 2.1667, "value": 3.23, "curve": [2.21, 1.83, 2.246, 24.47]}, {"time": 2.2667, "value": 24.47, "curve": [2.307, 24.47, 2.353, -21.43]}, {"time": 2.4, "value": -20.14, "curve": [2.445, -18.94, 2.49, 3.77]}, {"time": 2.5333, "value": 5.35, "curve": [2.649, 9.41, 2.755, 14.32]}, {"time": 2.8, "value": 14.32, "curve": [2.838, 14.32, 2.916, 11.85]}, {"time": 3, "value": 8.91}], "translate": [{"x": -0.52, "y": 1.02, "curve": [0.119, -0.29, 0.249, 0, 0.119, 0.56, 0.249, 0]}, {"time": 0.3, "curve": [0.467, 0, 0.723, -0.84, 0.467, 0, 0.723, 1.64]}, {"time": 0.8, "x": -0.84, "y": 1.64, "curve": [0.838, -0.84, 0.933, -0.66, 0.838, 1.64, 0.933, 1.28]}, {"time": 1, "x": -0.52, "y": 1.02, "curve": [1.119, -0.29, 1.249, 0, 1.119, 0.56, 1.249, 0]}, {"time": 1.3, "curve": [1.411, 0, 1.564, -0.46, 1.411, 0, 1.564, 0.89]}, {"time": 1.6667, "x": -0.68, "y": 1.33, "curve": [1.71, -0.78, 1.745, -0.84, 1.71, 1.51, 1.745, 1.64]}, {"time": 1.7667, "x": -0.84, "y": 1.64, "curve": [1.794, -0.84, 1.845, -0.81, 1.794, 1.64, 1.845, 1.58]}, {"time": 1.9, "x": -0.71, "y": 1.39, "curve": [1.934, -0.65, 1.97, -0.58, 1.934, 1.27, 1.97, 1.14]}, {"time": 2, "x": -0.52, "y": 1.02, "curve": [2.106, -0.31, 2.221, 0, 2.106, 0.61, 2.221, 0]}, {"time": 2.2667, "curve": [2.444, 0, 2.718, -0.84, 2.444, 0, 2.718, 1.64]}, {"time": 2.8, "x": -0.84, "y": 1.64, "curve": [2.838, -0.84, 2.916, -0.7, 2.838, 1.64, 2.916, 1.35]}, {"time": 3, "x": -0.52, "y": 1.02}]}, "bone18": {"rotate": [{"value": 0.37, "curve": [0.034, 0.15, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 3.59]}, {"time": 0.6, "value": 3.59, "curve": [0.734, 3.59, 0.867, 1.24]}, {"time": 1, "value": 0.37, "curve": [1.034, 0.15, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, 3.59]}, {"time": 1.6, "value": 3.59, "curve": [1.734, 3.59, 1.867, 1.24]}, {"time": 2, "value": 0.37, "curve": [2.034, 0.15, 2.067, 0]}, {"time": 2.1, "curve": [2.267, 0, 2.433, 3.59]}, {"time": 2.6, "value": 3.59, "curve": [2.734, 3.59, 2.868, 1.28]}, {"time": 3, "value": 0.37}], "translate": [{"x": -0.05, "y": 0.16, "curve": [0.034, -0.02, 0.067, 0, 0.034, 0.07, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, -0.51, 0.267, 0, 0.433, 1.55]}, {"time": 0.6, "x": -0.51, "y": 1.55, "curve": [0.734, -0.51, 0.867, -0.18, 0.734, 1.55, 0.867, 0.54]}, {"time": 1, "x": -0.05, "y": 0.16, "curve": [1.034, -0.02, 1.067, 0, 1.034, 0.07, 1.067, 0]}, {"time": 1.1, "curve": [1.267, 0, 1.433, -0.51, 1.267, 0, 1.433, 1.55]}, {"time": 1.6, "x": -0.51, "y": 1.55, "curve": [1.633, -0.51, 1.667, -0.49, 1.633, 1.55, 1.667, 1.49]}, {"time": 1.7, "x": -0.45, "y": 1.38, "curve": [1.722, -0.43, 1.745, -1.08, 1.722, 1.32, 1.745, -1.46]}, {"time": 1.7667, "x": -1.05, "y": -1.55, "curve": "stepped"}, {"time": 2.1667, "x": -1.05, "y": -1.55, "curve": [2.177, -1.03, 2.211, 0, 2.177, -1.6, 2.211, 0]}, {"time": 2.2333, "curve": [2.252, 0, 2.478, -0.51, 2.252, 0, 2.478, 1.55]}, {"time": 2.6, "x": -0.51, "y": 1.55, "curve": [2.734, -0.51, 2.868, -0.18, 2.734, 1.55, 2.868, 0.56]}, {"time": 3, "x": -0.05, "y": 0.16}], "scale": [{"time": 1.7333}, {"time": 1.8, "x": 1.11, "y": 1.11}, {"time": 1.8667}]}, "bone19": {"translate": [{"time": 1.7}, {"time": 1.7333, "x": -17.4, "y": -15.85, "curve": "stepped"}, {"time": 2.1667, "x": -17.4, "y": -15.85}, {"time": 2.2}]}, "bone20": {"rotate": [{"value": 0.26, "curve": [0.034, 0.11, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 2.51]}, {"time": 0.6, "value": 2.51, "curve": [0.734, 2.51, 0.867, 0.86]}, {"time": 1, "value": 0.26, "curve": [1.034, 0.11, 1.067, 0]}, {"time": 1.1, "curve": [1.271, 0, 1.462, 2.51]}, {"time": 1.6333, "value": 2.51, "curve": [1.767, 2.51, 1.788, 0.23]}, {"time": 2.0333, "value": 0.26, "curve": [2.068, 0.26, 2.072, 0]}, {"time": 2.1333, "curve": [2.3, 0.01, 2.327, 2.54]}, {"time": 2.6333, "value": 2.51, "curve": [2.761, 2.5, 2.775, 0.23]}, {"time": 3, "value": 0.26}], "translate": [{"x": 0.13, "y": 0.1, "curve": [0.034, 0.05, 0.067, 0, 0.034, 0.04, 0.067, 0]}, {"time": 0.1, "curve": [0.267, 0, 0.433, 1.28, 0.267, 0, 0.433, 1]}, {"time": 0.6, "x": 1.28, "y": 1, "curve": [0.734, 1.28, 0.867, 0.44, 0.734, 1, 0.867, 0.35]}, {"time": 1, "x": 0.13, "y": 0.1, "curve": [1.034, 0.05, 1.067, 0, 1.034, 0.04, 1.067, 0]}, {"time": 1.1, "curve": [1.268, 0, 1.465, 1.28, 1.268, 0, 1.465, 1]}, {"time": 1.6333, "x": 1.28, "y": 1, "curve": [1.667, 1.28, 1.672, 1.14, 1.667, 1, 1.672, 0.89]}, {"time": 1.7333, "x": 1.14, "y": 0.89, "curve": [1.756, 1.14, 1.759, 0.8, 1.756, 0.89, 1.759, 4.79]}, {"time": 1.8, "x": 0.81, "y": 4.75, "curve": [1.816, 0.81, 2.184, 0.86, 1.816, 4.73, 2.184, 4.79]}, {"time": 2.2, "x": 0.81, "y": 4.75, "curve": [2.21, 0.77, 2.226, -0.01, 2.21, 4.72, 2.226, -0.06]}, {"time": 2.2667, "curve": [2.285, 0, 2.409, 1.29, 2.285, 0.03, 2.409, 1.01]}, {"time": 2.6333, "x": 1.28, "y": 1, "curve": [2.762, 1.27, 2.775, 0.12, 2.762, 1, 2.775, 0.09]}, {"time": 3, "x": 0.13, "y": 0.1}], "scale": [{"time": 1.7667, "curve": [1.789, 1, 1.792, 1.111, 1.789, 1, 1.792, 1.111]}, {"time": 1.8333, "x": 1.11, "y": 1.11, "curve": [1.856, 1.109, 1.859, 0.999, 1.856, 1.109, 1.859, 0.999]}, {"time": 1.9, "curve": [1.944, 1.001, 1.989, 1, 1.944, 1.001, 1.989, 1]}, {"time": 2.0333}]}, "bone21": {"translate": [{"time": 1.7333, "curve": [1.744, 0, 1.746, 3.56, 1.744, 0, 1.746, 24.7]}, {"time": 1.7667, "x": 3.52, "y": 24.41, "curve": [1.911, 3.23, 2.056, 3.52, 1.911, 22.4, 2.056, 24.41]}, {"time": 2.2, "x": 3.52, "y": 24.41, "curve": [2.211, 3.52, 2.213, -0.04, 2.211, 24.41, 2.213, -0.28]}, {"time": 2.2333, "curve": [2.333, 0.2, 2.433, 0, 2.333, 1.39, 2.433, 0]}, {"time": 2.5333}]}, "bone22": {"rotate": [{"curve": [0.167, 0, 0.333, -5.58]}, {"time": 0.5, "value": -5.58, "curve": [0.667, -5.58, 0.833, 0]}, {"time": 1, "curve": [1.167, 0, 1.333, -5.58]}, {"time": 1.5, "value": -5.58, "curve": [1.667, -5.58, 1.833, 0]}, {"time": 2, "curve": [2.167, 0, 2.333, -5.58]}, {"time": 2.5, "value": -5.58, "curve": [2.667, -5.58, 2.833, 0]}, {"time": 3}]}, "bone24": {"translate": [{"time": 1.5}, {"time": 1.5667, "x": 24.44, "y": 0.68}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7667, "x": 31.13, "y": 0.86}, {"time": 2}]}, "bone25": {"translate": [{"time": 1.5}, {"time": 1.5667, "x": -9.24, "y": -0.35}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7333, "x": -2.7, "y": -1.21}, {"time": 1.7667, "x": -9.24, "y": -0.35}, {"time": 2}]}, "bone26": {"translate": [{"time": 1.5}, {"time": 1.5667, "x": 22.39}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7667, "x": 32.76, "y": 0.01}, {"time": 2}]}, "bone27": {"translate": [{"time": 1.5333, "curve": [1.556, 0, 1.559, 22.05, 1.556, 0, 1.559, 2.33]}, {"time": 1.6, "x": 21.8, "y": 2.31, "curve": [1.622, 21.66, 1.626, -0.25, 1.622, 2.29, 1.626, -0.03]}, {"time": 1.6667, "curve": [1.689, 0.14, 1.711, 0, 1.689, 0.01, 1.711, 0]}, {"time": 1.7333, "curve": [1.756, 0, 1.759, 22.05, 1.756, 0, 1.759, 2.33]}, {"time": 1.8, "x": 21.8, "y": 2.31, "curve": [1.878, 21.31, 1.89, -0.25, 1.878, 2.26, 1.89, -0.03]}, {"time": 2.0333, "curve": [2.2, 0.3, 2.367, 0, 2.2, 0.03, 2.367, 0]}, {"time": 2.5333}]}, "bone28": {"translate": [{"time": 0.5}, {"time": 0.8, "x": 1.72, "y": 7.01}, {"time": 1, "curve": "stepped"}, {"time": 1.5333, "curve": [1.556, 0, 1.559, -12.69, 1.556, 0, 1.559, -0.28]}, {"time": 1.6, "x": -12.54, "y": -0.28, "curve": [1.622, -12.46, 1.626, 0.15, 1.622, -0.28, 1.626, 0]}, {"time": 1.6667, "curve": [1.689, -0.08, 1.711, 0, 1.689, 0, 1.711, 0]}, {"time": 1.7333, "curve": [1.756, 0, 1.759, -12.69, 1.756, 0, 1.759, -0.28]}, {"time": 1.8, "x": -12.54, "y": -0.28, "curve": [1.878, -12.27, 1.89, 0.15, 1.878, -0.27, 1.89, 0]}, {"time": 2.0333, "curve": [2.2, -0.17, 2.367, 0, 2.2, 0, 2.367, 0]}, {"time": 2.5333}]}, "bone29": {"translate": [{"time": 1.5333, "curve": [1.556, 0, 1.559, 21.6, 1.556, 0, 1.559, 3.06]}, {"time": 1.6, "x": 21.35, "y": 3.02, "curve": [1.622, 21.22, 1.626, -0.25, 1.622, 3, 1.626, -0.04]}, {"time": 1.6667, "curve": [1.689, 0.14, 1.711, 0, 1.689, 0.02, 1.711, 0]}, {"time": 1.7333, "curve": [1.756, 0, 1.759, 23.7, 1.756, 0, 1.778, 0]}, {"time": 1.8, "x": 23.43, "curve": [1.878, 22.91, 1.89, -0.27, 1.878, 0, 1.956, 0]}, {"time": 2.0333, "curve": [2.2, 0.32, 2.367, 0, 2.2, 0, 2.367, 0]}, {"time": 2.5333}]}}, "attachments": {"default": {"Layer 5 copy": {"Layer 5 copy": {"deform": [{"time": 1.6, "vertices": [-2.41545, -6.08836, 1.76128, 6.30893, 6.98282, -3.62426, 0.34798, -1.98686, -0.31793, 6.49741, -4.08154, 5.77034, 0, 0, 11.61711, 1.4871, 8.83055, 2.97897, -8.46738, -3.89215, 3.05855, -3.11109, 0, 0, 2.91695, 3.4925, 3.34309, 0.7899, -2.22864, -0.52657, -0.74489, -3.54334], "curve": [1.622, 0, 1.626, 1.01]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7333, "curve": [1.755, -0.01, 1.759, 1.01]}, {"time": 1.8, "vertices": [-2.41545, -6.08836, 1.76128, 6.30893, 6.98282, -3.62426, 0.34798, -1.98686, -0.31793, 6.49741, -4.08154, 5.77034, 0, 0, 11.61711, 1.4871, 8.83055, 2.97897, -8.46738, -3.89215, 3.05855, -3.11109, 0, 0, 2.91695, 3.4925, 3.34309, 0.7899, -2.22864, -0.52657, -0.74489, -3.54334]}]}}, "Layer 6 copy": {"Layer 6 copy": {"deform": [{"time": 1.5667, "vertices": [-1.3331, -10.12752, 1.05139, 10.16056, 1.9695, -4.23228, -2.0863, 4.1758, 0.7713, -0.92293, -0.79668, 0.90111, -0.16837, 10.04835, 0.44722, -10.0399, -7.46368, 9.63367, -8.56926, 2.04752, 8.62279, -1.8089, 5.94856, -0.31877, -5.95532, 0.1534, -0.27203, 1.73211, 0.31996, -1.72396, -1.47026, 5.04136, 1.60951, -4.9989, 0.99583, -1.12644, 1.63586, 9.27242, -2.94598, 0.47707, 2.9581, -0.39513, -3.05965, -0.79364, -0.53395, -3.5813, 0.43428, 3.59464]}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7667, "vertices": [-1.3331, -10.12752, 1.05139, 10.16056, 1.9695, -4.23228, -2.0863, 4.1758, 0.7713, -0.92293, -0.79668, 0.90111, -0.16837, 10.04835, 0.44722, -10.0399, -7.46368, 9.63367, -8.56926, 2.04752, 8.62279, -1.8089, 5.94856, -0.31877, -5.95532, 0.1534, -0.27203, 1.73211, 0.31996, -1.72396, -1.47026, 5.04136, 1.60951, -4.9989, 0.99583, -1.12644, 1.63586, 9.27242, -2.94598, 0.47707, 2.9581, -0.39513, -3.05965, -0.79364, -0.53395, -3.5813, 0.43428, 3.59464]}]}}, "Layer 7": {"Layer 7": {"deform": [{"time": 1.7667, "curve": [1.789, 0, 1.792, 1.01]}, {"time": 1.8333, "vertices": [2.49551, 2e-05, 2.49551, 2e-05, 2.49551, 2e-05, 2.49551], "curve": [1.855, -0.01, 1.859, 1.01]}, {"time": 1.9}]}}, "Layer 8": {"Layer 8": {"deform": [{"time": 1.7333}, {"time": 1.8, "vertices": [3.19286, -2e-05, 3.19286, -2e-05, 3.19286, -2e-05, 3.19286]}, {"time": 1.8667}]}}, "Layer 22": {"Layer 22": {"deform": [{"time": 1.5667, "offset": 4, "vertices": [3.20947, 0.22854, 3.2095, 0.22851, 5.31865, -0.11428, 5.31865, -0.11432, 5.98138, 0.27101, 9.08641, -1.19828, 6.55276, -0.8293, 5.00189, -1.79901, 5.00189, -1.79908, 0, 0, 0, 0, 0, 0, 0, 0, 7.48004, 0.39182, 7.48007, 0.39173, 7.85225, -0.48322, 9.13863, -0.34935, 7.1895, -0.8685, 10.26501, 0.64646, 10.26511, 0.64637, 6.48749, -1.89042, 6.48749, -1.89052]}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7667, "offset": 4, "vertices": [3.20947, 0.22854, 3.2095, 0.22851, 5.31865, -0.11428, 5.31865, -0.11432, 5.98138, 0.27101, 9.08641, -1.19828, 6.55276, -0.8293, 5.00189, -1.79901, 5.00189, -1.79908, 0, 0, 0, 0, 0, 0, 0, 0, 7.48004, 0.39182, 7.48007, 0.39173, 7.85225, -0.48322, 9.13863, -0.34935, 7.1895, -0.8685, 10.26501, 0.64646, 10.26511, 0.64637, 6.48749, -1.89042, 6.48749, -1.89052]}]}}, "Layer 23": {"Layer 23": {"deform": [{"time": 1.5667, "vertices": [-0.91257, 5.8601, -1.13306, 5.82143, 0, 0, 0.62355, -0.25143, 1.3979, -3.13057, 1.515, -3.07565, 0.31898, -3.23686, -0.06208, -2.77156, -2.75054, 0.27371, -3.19475, 0.10424, -4.4642, 0.23056, -3.61792, 0.14635, -1.43997, 2.70741, 1.18636, -3.10948, 1.92528, 0.02212, 1.60846, -1.01475, 0.5916, 1.65056]}, {"time": 1.6333, "curve": "stepped"}, {"time": 1.7}, {"time": 1.7667, "vertices": [-0.91257, 5.8601, -1.13306, 5.82143, 0, 0, 0.62355, -0.25143, 1.3979, -3.13057, 1.515, -3.07565, 0.31898, -3.23686, -0.06208, -2.77156, -2.75054, 0.27371, -3.19475, 0.10424, -4.4642, 0.23056, -3.61792, 0.14635, -1.43997, 2.70741, 1.18636, -3.10948, 1.92528, 0.02212, 1.60846, -1.01475, 0.5916, 1.65056]}]}}, "Layer 24": {"Layer 24": {"deform": [{"time": 1.6, "vertices": [-0.5276, 2.57658, -0.52759, 2.57657, 6.40027, 0.36309, 6.40027, 0.36308, 3.37144, -0.78352, 7.30481, 1.60114, 7.30486, 1.60111, 0, 0, 0, 0, 2.11516, -0.30383, 2.11516, -0.30384, 8.76437, 0.89985, 8.76439, 0.89983, 4.02699, -0.38608, 3.17728, -0.34946, 5.33812, 0.40872, 5.33818, 0.40869, 1.04385, -0.47055, 1.04387, -0.47055, -2.70679, 1.39342, -2.70674, 1.39341], "curve": [1.622, 0, 1.626, 1.01]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7333, "curve": [1.755, -0.01, 1.759, 1.01]}, {"time": 1.8, "vertices": [-0.5276, 2.57658, -0.52759, 2.57657, 6.40027, 0.36309, 6.40027, 0.36308, 3.37144, -0.78352, 7.30481, 1.60114, 7.30486, 1.60111, 0, 0, 0, 0, 2.11516, -0.30383, 2.11516, -0.30384, 8.76437, 0.89985, 8.76439, 0.89983, 4.02699, -0.38608, 3.17728, -0.34946, 5.33812, 0.40872, 5.33818, 0.40869, 1.04385, -0.47055, 1.04387, -0.47055, -2.70679, 1.39342, -2.70674, 1.39341]}]}}, "Layer 25": {"Layer 25": {"deform": [{"time": 1.6, "vertices": [-1.49281, 1.69706, 2.22482, 2.24538, 1.1832, -2.17912, 0, 0, 0, 0, 2.50227, -0.54298, 0, 0, 0, 0, 0, 0, 0, 0, -2.17397, -1.85922, -3.67739, -1.87634, -0.11723, 2.37432, 0.39416, -0.26622, 0, 0, -0.74311, -0.76028], "curve": [1.622, 0, 1.626, 1.01]}, {"time": 1.6667, "curve": "stepped"}, {"time": 1.7333, "curve": [1.755, -0.01, 1.759, 1.01]}, {"time": 1.8, "vertices": [-1.49281, 1.69706, 2.22482, 2.24538, 1.1832, -2.17912, 0, 0, 0, 0, 2.50227, -0.54298, 0, 0, 0, 0, 0, 0, 0, 0, -2.17397, -1.85922, -3.67739, -1.87634, -0.11723, 2.37432, 0.39416, -0.26622, 0, 0, -0.74311, -0.76028]}]}}}}}, "idle_back": {"slots": {"778887899898": {"attachment": [{"name": "778887899898"}]}, "988998675654": {"attachment": [{"name": "988998675654"}]}, "beak-down": {"attachment": [{}]}, "beak-up": {"attachment": [{}]}, "body": {"attachment": [{}]}, "crest": {"attachment": [{}]}, "Layer 3": {"attachment": [{}]}, "Layer 4": {"attachment": [{}]}, "Layer 5": {"attachment": [{}]}, "Layer 6": {"attachment": [{}]}, "Layer 7": {"attachment": [{}]}, "Layer 8": {"attachment": [{}]}, "Layer 9": {"attachment": [{}]}, "Layer 10": {"attachment": [{}]}, "Layer 11": {"attachment": [{}]}, "Layer 12": {"attachment": [{}]}, "Layer 13": {"attachment": [{}]}, "Layer 14": {"attachment": [{}]}, "Layer 15": {"attachment": [{}]}, "Layer 16": {"attachment": [{}]}, "Layer 17": {"attachment": [{}]}, "Layer 18": {"attachment": [{}]}, "Layer 19": {"attachment": [{}]}, "Layer 20": {"attachment": [{}]}, "mouth-inside": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "wing-down": {"attachment": [{}]}, "wing-up": {"attachment": [{}]}, "yazik": {"attachment": [{}]}}, "bones": {"bone55": {"translate": [{"x": -169.02, "y": 88.45}]}}}, "idle_front": {"slots": {"566778878990": {"attachment": [{"name": "566778878990"}]}, "3344555656556": {"attachment": [{"name": "3344555656556"}]}, "beak-down": {"attachment": [{}]}, "beak-up": {"attachment": [{}]}, "body": {"attachment": [{}]}, "crest": {"attachment": [{}]}, "Layer 3": {"attachment": [{}]}, "Layer 4": {"attachment": [{}]}, "Layer 5": {"attachment": [{}]}, "Layer 6": {"attachment": [{}]}, "Layer 7": {"attachment": [{}]}, "Layer 8": {"attachment": [{}]}, "Layer 9": {"attachment": [{}]}, "Layer 10": {"attachment": [{}]}, "Layer 11": {"attachment": [{}]}, "Layer 12": {"attachment": [{}]}, "Layer 13": {"attachment": [{}]}, "Layer 14": {"attachment": [{}]}, "Layer 15": {"attachment": [{}]}, "Layer 16": {"attachment": [{}]}, "Layer 17": {"attachment": [{}]}, "Layer 18": {"attachment": [{}]}, "Layer 19": {"attachment": [{}]}, "Layer 20": {"attachment": [{}]}, "mouth-inside": {"attachment": [{}]}, "shadow": {"attachment": [{}]}, "wing-down": {"attachment": [{}]}, "wing-up": {"attachment": [{}]}, "yazik": {"attachment": [{}]}}, "bones": {"bone55": {"translate": [{"x": -169.02, "y": 88.45}]}}}, "jump": {"bones": {"bone2": {"rotate": [{}, {"time": 0.2667, "value": -14.32}, {"time": 0.5, "value": 9.24}, {"time": 0.5333}], "translate": [{}, {"time": 0.2667, "x": 5.99, "y": -15.97}, {"time": 0.3, "x": 18.43, "y": 60.71, "curve": [0.32, 15.8, 0.352, 7.95, 0.32, 64.52, 0.352, 76.02]}, {"time": 0.4, "x": 7.98, "y": 75.85, "curve": [0.457, 8.03, 0.483, 18.79, 0.457, 75.64, 0.483, 28.11]}, {"time": 0.5, "x": 20.92, "y": 18.67}, {"time": 0.5333, "y": -21.3}, {"time": 0.6667}], "scale": [{"time": 0.2667}, {"time": 0.3, "x": 1.386, "y": 0.889, "curve": [0.32, 1.289, 0.352, 0.999, 0.32, 0.917, 0.352, 1]}, {"time": 0.4, "curve": [0.457, 1.001, 0.483, 1.2, 0.457, 1, 0.483, 0.897]}, {"time": 0.5, "x": 1.24, "y": 0.876}, {"time": 0.5333}]}, "bone3": {"translate": [{"x": 0.29, "curve": "stepped"}, {"time": 0.2667, "x": 0.29}, {"time": 0.5, "x": 11.71, "y": 0.66}, {"time": 0.5333, "x": 0.29}, {"time": 0.6333, "x": -25.22, "y": -15.59}, {"time": 0.7667, "x": 0.29}]}, "bone4": {"translate": [{}, {"time": 0.2667, "x": -17.94, "y": -26.55}, {"time": 0.3, "x": -22.26, "y": 4.47, "curve": [0.333, -5.1, 0.387, 45.87, 0.333, 13.49, 0.387, 40.29]}, {"time": 0.4667, "x": 45.87, "y": 40.29}, {"time": 0.5667, "x": -14.44, "y": -3.46}, {"time": 0.7333}]}, "bone5": {"translate": [{}, {"time": 0.2667, "x": -12.6, "y": -9.26}, {"time": 0.3, "x": -18.05, "y": -32.06, "curve": [0.333, -3.06, 0.387, 41.46, 0.333, -28.16, 0.387, -16.59]}, {"time": 0.4667, "x": 41.46, "y": -16.59}, {"time": 0.5667, "x": -23.95, "y": -7.98}, {"time": 0.7333}]}, "bone6": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": -2.79, "curve": [0.32, -5.29, 0.352, -12.84]}, {"time": 0.4, "value": -12.73, "curve": [0.457, -12.61, 0.483, 15.5]}, {"time": 0.5, "value": 21.09}, {"time": 0.5333}], "translate": [{"time": 0.2667}, {"time": 0.3, "x": -3.56, "y": 26.78, "curve": [0.32, -16.7, 0.352, -56.02, 0.32, 37.67, 0.352, 70.21]}, {"time": 0.4, "x": -55.75, "y": 70.01, "curve": [0.457, -55.42, 0.483, 19.94, 0.457, 69.77, 0.483, 14.87]}, {"time": 0.5, "x": 34.9, "y": 3.97}, {"time": 0.5333}]}, "bone7": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": -41.3, "curve": [0.32, -43.18, 0.352, -48.97]}, {"time": 0.4, "value": -48.77, "curve": [0.457, -48.53, 0.483, 7.17]}, {"time": 0.5, "value": 18.22}, {"time": 0.5333}]}, "bone8": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": -36.77, "curve": [0.32, -26.06, 0.352, 5.77]}, {"time": 0.4, "value": 5.76, "curve": [0.476, 5.74, 0.511, 0.95]}, {"time": 0.5333}], "translate": [{"time": 0.2667}, {"time": 0.3, "x": 2.2, "y": 24.31, "curve": [0.32, 12.12, 0.352, 41.59, 0.32, 33.34, 0.352, 60.37]}, {"time": 0.4, "x": 41.58, "y": 60.18, "curve": [0.457, 41.57, 0.483, 39.3, 0.457, 59.97, 0.483, 8.93]}, {"time": 0.5, "x": 38.85, "y": -1.21}, {"time": 0.5333}]}, "bone9": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": -64.7, "curve": [0.32, -49.21, 0.352, -3.22]}, {"time": 0.4, "value": -3.16, "curve": [0.457, -3.09, 0.483, 13.81]}, {"time": 0.5, "value": 17.16}, {"time": 0.5333}]}, "bone10": {"rotate": [{"value": 0.16, "curve": "stepped"}, {"time": 0.2667, "value": 0.16}, {"time": 0.3, "value": 26.63, "curve": [0.326, 14.67, 0.369, -20.87]}, {"time": 0.4333, "value": -20.86, "curve": [0.49, -20.85, 0.517, -18.15]}, {"time": 0.5333, "value": -17.61}, {"time": 0.7, "value": 9.85}, {"time": 0.8, "value": 0.16}], "translate": [{"x": -0.78, "y": 0.67, "curve": "stepped"}, {"time": 0.2667, "x": -0.78, "y": 0.67}, {"time": 0.3, "x": -13.94, "y": 9.37, "curve": [0.339, -8.46, 0.404, 7.83, 0.339, 8.25, 0.404, 4.92]}, {"time": 0.5, "x": 7.83, "y": 4.92}, {"time": 0.5333, "x": -0.78, "y": 0.67}, {"time": 0.7, "x": -14.68, "y": 2.94}, {"time": 0.8, "x": -0.78, "y": 0.67}]}, "bone11": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": 21.3, "curve": [0.32, 8.93, 0.352, -27.86]}, {"time": 0.4, "value": -27.83, "curve": [0.476, -27.78, 0.511, -15.98]}, {"time": 0.5333, "value": -13.64}, {"time": 0.7, "value": 26.47}, {"time": 0.8333}], "translate": [{"time": 0.2667}, {"time": 0.5, "x": -7.74, "y": 8.97}, {"time": 0.5333}]}, "bone12": {"rotate": [{"value": 6.98, "curve": "stepped"}, {"time": 0.2667, "value": 6.98}, {"time": 0.3, "value": 32.27, "curve": [0.32, 22.19, 0.352, -7.78]}, {"time": 0.4, "value": -7.78, "curve": [0.476, -7.78, 0.511, -7.15]}, {"time": 0.5333, "value": -7.03}, {"time": 0.7333, "value": 38.22}, {"time": 0.8667, "value": 6.98}], "translate": [{"x": -1.82, "y": 1.28, "curve": "stepped"}, {"time": 0.2667, "x": -1.82, "y": 1.28}, {"time": 0.5, "x": -7.13, "y": 7.01}, {"time": 0.5333, "x": -1.82, "y": 1.28}]}, "bone13": {"rotate": [{"value": 0.47, "curve": "stepped"}, {"time": 0.2667, "value": 0.47}, {"time": 0.5, "value": 11.47}, {"time": 0.5333, "value": 0.47}], "translate": [{"x": 0.35}, {"time": 0.2667, "x": 9.32, "y": -2.36}, {"time": 0.3, "x": -13.52, "y": -1.81, "curve": [0.339, -4.73, 0.404, 21.37, 0.339, -0.97, 0.404, 1.53]}, {"time": 0.5, "x": 21.37, "y": 1.53}, {"time": 0.5333, "x": 0.35}, {"time": 0.6, "x": -20.96, "y": -1.36}, {"time": 0.7, "x": 0.35}]}, "bone14": {"rotate": [{"time": 0.2667}, {"time": 0.3, "value": 21.09, "curve": [0.346, 22.94, 0.421, 28.44]}, {"time": 0.5333, "value": 28.44}, {"time": 0.6667, "value": -8.7}, {"time": 0.7667}]}, "bone15": {"rotate": [{}, {"time": 0.2667, "value": 6.33}, {"time": 0.3, "value": -22.82, "curve": [0.339, -23.24, 0.404, -24.49]}, {"time": 0.5, "value": -24.49}, {"time": 0.5333}, {"time": 0.6333, "value": 14.96}, {"time": 0.7333}]}, "bone17": {"rotate": [{"value": 8.91}, {"time": 0.2667, "value": -2.9}, {"time": 0.4333, "value": -26.17, "curve": [0.49, -26.33, 0.517, -63.24]}, {"time": 0.5333, "value": -70.57}, {"time": 0.6667, "value": 51.12}, {"time": 0.8, "value": 8.91}], "translate": [{"x": -0.52, "y": 1.02, "curve": "stepped"}, {"time": 0.2667, "x": -0.52, "y": 1.02}, {"time": 0.3, "x": 14.29, "y": 14.91, "curve": [0.326, 11.69, 0.369, 3.99, 0.326, 10.04, 0.369, -4.42]}, {"time": 0.4333, "x": 3.99, "y": -4.46, "curve": [0.49, 3.99, 0.517, 3.66, 0.49, -4.49, 0.517, -11.64]}, {"time": 0.5333, "x": 3.59, "y": -13.06}, {"time": 0.6667, "x": -3.69, "y": 13.93}, {"time": 0.8, "x": -0.52, "y": 1.02}]}, "bone18": {"rotate": [{"value": 0.37, "curve": "stepped"}, {"time": 0.2667, "value": 0.37}, {"time": 0.5, "value": 9.63}, {"time": 0.5333, "value": 0.37}], "translate": [{"x": -0.05, "y": 0.16}, {"time": 0.2667, "x": 9.39, "y": 1.11}, {"time": 0.3, "x": -12.18, "y": -3.05, "curve": [0.339, -7.23, 0.404, 7.5, 0.339, -1.89, 0.404, 1.58]}, {"time": 0.5, "x": 7.5, "y": 1.58}, {"time": 0.5333, "x": -0.05, "y": 0.16}, {"time": 0.6667, "x": -20.23, "y": -5.24}, {"time": 0.8, "x": -0.05, "y": 0.16}]}, "bone20": {"rotate": [{"value": 0.26}], "translate": [{"x": 0.13, "y": 0.1}, {"time": 0.2667, "x": 8.8, "y": 3.48}, {"time": 0.3, "x": -17.22, "y": -3.75, "curve": [0.339, -10.71, 0.404, 8.63, 0.339, -1.95, 0.404, 3.41]}, {"time": 0.5, "x": 8.63, "y": 3.41}, {"time": 0.5333, "x": 0.13, "y": 0.1}, {"time": 0.6667, "x": -12.15, "y": -2.26}, {"time": 0.8, "x": 0.13, "y": 0.1}]}, "bone22": {"rotate": [{}, {"time": 0.2667, "value": -15.6}, {"time": 0.3, "value": 27.23, "curve": [0.326, 23.19, 0.369, 11.23]}, {"time": 0.4333, "value": 11.19, "curve": [0.49, 11.15, 0.517, 1.85]}, {"time": 0.5333}, {"time": 0.6333, "value": 10.86}, {"time": 0.7333}], "translate": [{}, {"time": 0.2667, "x": 14.81, "y": 3.78}, {"time": 0.3, "x": -49.93, "y": 25.78, "curve": [0.326, -52.84, 0.369, 12.49, 0.326, 26.6, 0.369, 15.66]}, {"time": 0.4333, "x": 12.6, "y": 15.6, "curve": [0.49, 12.69, 0.517, 18.89, 0.49, 15.54, 0.517, 3.45]}, {"time": 0.5333, "x": 23.37, "y": 0.9}, {"time": 0.6333, "x": -10.88, "y": 0.43}, {"time": 0.7333}]}, "bone23": {"translate": [{}, {"time": 0.2667, "x": 19.35, "y": -14.88}, {"time": 0.4, "y": -28.27, "curve": [0.457, 0.05, 0.483, 11.96, 0.457, -28.26, 0.483, -24.25]}, {"time": 0.5, "x": 14.33, "y": -23.46}, {"time": 0.5333, "x": 2.23, "y": -19.87, "curve": [0.594, 2.2, 0.653, 0.15, 0.594, -19.58, 0.653, -1.3]}, {"time": 0.7}], "scale": [{}, {"time": 0.2667, "x": 1.109, "y": 1.109}, {"time": 0.3, "x": 0.694, "y": 0.694, "curve": [0.32, 0.662, 0.352, 0.568, 0.32, 0.662, 0.352, 0.568]}, {"time": 0.4, "x": 0.569, "y": 0.569, "curve": [0.457, 0.57, 0.483, 0.874, 0.457, 0.57, 0.483, 0.874]}, {"time": 0.5, "x": 0.934, "y": 0.934}, {"time": 0.5333, "x": 1.208, "y": 1.208, "curve": [0.594, 1.205, 0.653, 1.014, 0.594, 1.205, 0.653, 1.014]}, {"time": 0.7}]}, "root": {"translate": [{"time": 1}]}}}, "win": {"slots": {"$": {"attachment": [{"time": 0.4667, "name": "$"}, {"time": 0.9333}]}, "$2": {"attachment": [{"time": 0.4667, "name": "$"}, {"time": 0.9333}]}, "11112222211234": {"attachment": [{"time": 0.4667, "name": "11112222211234"}, {"time": 0.9333}]}, "1111122222333123": {"attachment": [{"time": 0.4667, "name": "1111122222333123"}, {"time": 0.9333}]}, "Layer 3": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 3"}]}, "Layer 4": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 4"}]}, "Layer 5": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 5"}]}, "Layer 6": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 6"}]}, "Layer 7": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 7"}]}, "Layer 8": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 8"}]}, "Layer 9": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 9"}]}, "Layer 10": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 10"}]}, "Layer 19": {"attachment": [{"time": 0.4667}, {"time": 0.9333, "name": "Layer 19"}]}}, "bones": {"bone12": {"rotate": [{"value": 6.98, "curve": "stepped"}, {"time": 0.2667, "value": 6.98, "curve": [0.278, 12.88, 0.286, 33.53]}, {"time": 0.3, "value": 32.27, "curve": [0.404, 22.94, 0.479, -7.73]}, {"time": 0.6, "value": -7.78, "curve": [0.687, -7.81, 0.825, -7.47]}, {"time": 0.9333, "value": -7.03}, {"time": 1.1333, "value": 38.22}, {"time": 1.2667, "value": 6.98}], "translate": [{"x": -1.82, "y": 1.28, "curve": "stepped"}, {"time": 0.2667, "x": -1.82, "y": 1.28, "curve": [0.486, -3.05, 0.644, -7.48, 0.486, 2.62, 0.644, 7.39]}, {"time": 0.9, "x": -7.13, "y": 7.01, "curve": "stepped"}, {"time": 0.9333, "x": -1.82, "y": 1.28}], "scale": [{"time": 0.2667, "curve": [0.378, 1, 0.489, 1.014, 0.378, 1, 0.489, 1]}, {"time": 0.6, "curve": [0.678, 0.99, 0.803, 1.086, 0.7, 1, 0.8, 1]}, {"time": 0.9, "x": 1.211, "curve": "stepped"}, {"time": 0.9333}]}, "bone10": {"rotate": [{"value": 0.16, "curve": "stepped"}, {"time": 0.2667, "value": 0.16, "curve": [0.278, 6.33, 0.286, 27.97]}, {"time": 0.3, "value": 26.63, "curve": [0.415, 15.56, 0.498, -20.6]}, {"time": 0.6333, "value": -20.86, "curve": [0.712, -21.01, 0.836, -19.54]}, {"time": 0.9333, "value": -17.61}, {"time": 1.1, "value": 9.85}, {"time": 1.2, "value": 0.16}], "translate": [{"x": -0.78, "y": 0.67, "curve": "stepped"}, {"time": 0.2667, "x": -0.78, "y": 0.67, "curve": [0.278, -6.98, 0.285, -27.98, 0.278, 5.82, 0.286, 23.3]}, {"time": 0.3, "x": -27.35, "y": 22.78, "curve": [0.415, -22.42, 0.499, -5.3, 0.415, 18.61, 0.499, 3.78]}, {"time": 0.6333, "x": -6.2, "y": 4.88, "curve": [0.703, -6.67, 0.813, -2.14, 0.703, 5.44, 0.813, -0.08]}, {"time": 0.9, "x": 3.76, "y": -7.28, "curve": "stepped"}, {"time": 0.9333, "x": -0.78, "y": 0.67}, {"time": 1.1, "x": -14.68, "y": 2.94}, {"time": 1.2, "x": -0.78, "y": 0.67}], "scale": [{"time": 0.2667, "curve": [0.389, 1, 0.511, 1.012, 0.389, 1, 0.511, 1]}, {"time": 0.6333, "curve": [0.703, 0.993, 0.813, 1.061, 0.722, 1, 0.811, 1]}, {"time": 0.9, "x": 1.15, "curve": "stepped"}, {"time": 0.9333}]}, "bone11": {"rotate": [{"time": 0.2667, "curve": [0.278, 4.96, 0.287, 22.77]}, {"time": 0.3, "value": 21.3, "curve": [0.404, 9.85, 0.478, -26.9]}, {"time": 0.6, "value": -27.83, "curve": [0.687, -28.49, 0.825, -22.05]}, {"time": 0.9333, "value": -13.64}, {"time": 1.1, "value": 26.47}, {"time": 1.2333}], "translate": [{"time": 0.2667, "curve": [0.486, -1.8, 0.644, -8.25, 0.486, 2.09, 0.644, 9.56]}, {"time": 0.9, "x": -7.74, "y": 8.97, "curve": "stepped"}, {"time": 0.9333}], "scale": [{"time": 0.2667, "curve": [0.378, 1, 0.489, 1.015, 0.378, 1, 0.489, 1]}, {"time": 0.6, "curve": [0.678, 0.99, 0.803, 1.091, 0.7, 1, 0.8, 1]}, {"time": 0.9, "x": 1.224, "curve": "stepped"}, {"time": 0.9333}]}, "bone19": {"translate": [{"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "x": 52.24, "y": -61.52}, {"time": 0.9, "x": 57.17, "y": -60.62, "curve": "stepped"}, {"time": 0.9333}], "scale": [{"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "x": 0.253, "y": 0.253}, {"time": 0.6, "x": 1.101, "y": 1.101, "curve": "stepped"}, {"time": 0.8333, "x": 1.101, "y": 1.101}, {"time": 0.9, "x": 0.524, "y": 0.524, "curve": "stepped"}, {"time": 0.9333}]}, "bone22": {"rotate": [{}, {"time": 0.2667, "value": -15.6, "curve": [0.278, -5.62, 0.283, 27.77]}, {"time": 0.3, "value": 27.23, "curve": [0.415, 23.49, 0.499, 10.29]}, {"time": 0.6333, "value": 11.19, "curve": [0.712, 11.71, 0.836, 6.63]}, {"time": 0.9333}, {"time": 1.0333, "value": 10.86}, {"time": 1.1333}], "translate": [{}, {"time": 0.2667, "x": 14.81, "y": 3.78, "curve": [0.278, -0.28, 0.282, -52.26, 0.278, 8.91, 0.286, 26.08]}, {"time": 0.3, "x": -49.93, "y": 25.78, "curve": [0.415, -35.36, 0.498, 13.47, 0.415, 23.41, 0.499, 14.42]}, {"time": 0.6333, "x": 12.6, "y": 15.6, "curve": [0.712, 12.09, 0.836, 16.99, 0.712, 16.28, 0.836, 9.61]}, {"time": 0.9333, "x": 23.37, "y": 0.9}, {"time": 1.0333, "x": -10.88, "y": 0.43}, {"time": 1.1333}]}, "bone2": {"rotate": [{}, {"time": 0.2667, "value": -14.32, "curve": [0.486, -8.83, 0.644, 10.78]}, {"time": 0.9, "value": 9.24, "curve": "stepped"}, {"time": 0.9333}], "translate": [{}, {"time": 0.2667, "x": 5.99, "y": -15.97, "curve": [0.278, 8.89, 0.286, 18.75, 0.278, 1.9, 0.279, 58.27]}, {"time": 0.3, "x": 18.43, "y": 60.71, "curve": [0.404, 16, 0.479, 8.92, 0.404, 72.98, 0.481, 106.63]}, {"time": 0.6, "x": 7.98, "y": 113.37, "curve": [0.678, 7.38, 0.803, 13.26, 0.678, 117.8, 0.803, 74.78]}, {"time": 0.9, "x": 20.92, "y": 18.67, "curve": "stepped"}, {"time": 0.9333, "y": -21.3}, {"time": 1.0667}], "scale": [{"time": 0.2667, "curve": [0.278, 1.09, 0.285, 1.4, 0.278, 0.974, 0.286, 0.885]}, {"time": 0.3, "x": 1.386, "y": 0.889, "curve": [0.404, 1.296, 0.479, 1.017, 0.404, 0.915, 0.479, 0.991]}, {"time": 0.6, "curve": [0.678, 0.989, 0.803, 1.098, 0.678, 1.006, 0.803, 0.95]}, {"time": 0.9, "x": 1.24, "y": 0.876, "curve": "stepped"}, {"time": 0.9333}]}, "bone18": {"rotate": [{"value": 0.37, "curve": "stepped"}, {"time": 0.2667, "value": 0.37, "curve": [0.338, 1.08, 0.403, 27.23]}, {"time": 0.4667, "value": 32.06, "curve": [0.511, 35.41, 0.555, 18.07]}, {"time": 0.6, "value": 18.06, "curve": [0.66, 18.03, 0.715, 17.47]}, {"time": 0.7333, "value": 16.82, "curve": [0.793, 14.74, 0.827, 29.44]}, {"time": 0.9, "value": 29.26, "curve": "stepped"}, {"time": 0.9333, "value": 0.37}], "translate": [{"x": -0.05, "y": 0.16}, {"time": 0.2667, "x": 9.39, "y": 1.11, "curve": [0.278, 4.37, 0.286, -12.89, 0.278, 0.14, 0.287, -3.11]}, {"time": 0.3, "x": -12.18, "y": -3.05, "curve": [0.348, -9.75, 0.391, 30.1, 0.348, -2.86, 0.39, -5.95]}, {"time": 0.4333, "x": 39.5, "y": -7.27, "curve": "stepped"}, {"time": 0.4667, "x": 14.23, "y": 3.72, "curve": [0.507, 23.26, 0.55, 28.59, 0.507, 2.4, 0.55, -11.26]}, {"time": 0.6, "x": 28.59, "y": -11.25, "curve": [0.662, 28.58, 0.715, 26.48, 0.662, -11.24, 0.715, -10.53]}, {"time": 0.7333, "x": 25.02, "y": -7.83}, {"time": 0.9, "x": 7.34, "y": 0.95, "curve": "stepped"}, {"time": 0.9333, "x": 23.49, "y": -0.96}, {"time": 1, "x": -10.14, "y": -2.54}, {"time": 1.0667, "x": -20.23, "y": -5.24}, {"time": 1.2, "x": -0.05, "y": 0.16}], "scale": [{"time": 0.2667, "curve": [0.323, 1, 0.379, 1.099, 0.323, 1, 0.379, 1.099]}, {"time": 0.4333, "x": 1.099, "y": 1.099, "curve": "stepped"}, {"time": 0.4667, "x": 0.749, "y": 0.764, "curve": [0.512, 0.749, 0.556, 1, 0.512, 0.764, 0.556, 1]}, {"time": 0.6, "curve": [0.644, 1, 0.715, 1.009, 0.644, 1, 0.689, 1]}, {"time": 0.7333, "x": 1.02}, {"time": 0.9, "x": 0.731, "y": 0.731, "curve": "stepped"}, {"time": 0.9333}]}, "bone3": {"rotate": [{"time": 0.2667, "curve": [0.378, 0, 0.489, 12.83]}, {"time": 0.6, "value": 12.83, "curve": [0.711, 12.83, 0.822, 0]}, {"time": 0.9333}], "translate": [{"x": 0.29, "curve": "stepped"}, {"time": 0.2667, "x": 0.29, "curve": [0.388, 1.76, 0.491, 11.59, 0.388, 0.09, 0.491, 5.59]}, {"time": 0.6, "x": 14.36, "y": 5.75, "curve": [0.691, 16.6, 0.786, 12.04, 0.691, 5.88, 0.786, 0.68]}, {"time": 0.9, "x": 11.71, "y": 0.66, "curve": "stepped"}, {"time": 0.9333, "x": 0.29}, {"time": 1.0333, "x": -25.22, "y": -15.59}, {"time": 1.1667, "x": 0.29}]}, "bone4": {"rotate": [{"time": 0.3, "curve": [0.415, -5.69, 0.499, -22.65]}, {"time": 0.6333, "value": -24.41, "curve": [0.72, -25.55, 0.858, -14.46]}, {"time": 0.9667}], "translate": [{}, {"time": 0.2667, "x": -17.94, "y": -26.55, "curve": [0.278, -18.95, 0.289, -24.53, 0.278, -19.32, 0.286, 3]}, {"time": 0.3, "x": -22.26, "y": 4.47, "curve": [0.415, 1.96, 0.497, 77.93, 0.415, 16.3, 0.498, 53.69]}, {"time": 0.6333, "x": 81.67, "y": 55.24, "curve": [0.694, 83.35, 0.791, 67.08, 0.694, 55.94, 0.791, 49.15]}, {"time": 0.8667, "x": 45.87, "y": 40.29}, {"time": 0.9667, "x": -14.44, "y": -3.46}, {"time": 1.1333}]}, "bone8": {"rotate": [{"time": 0.2667, "curve": [0.278, -8.57, 0.286, -38.71]}, {"time": 0.3, "value": -36.77, "curve": [0.404, -22.81, 0.479, 18.89]}, {"time": 0.6, "value": 23.14, "curve": [0.678, 25.9, 0.803, -0.87]}, {"time": 0.9, "value": -35.77, "curve": "stepped"}, {"time": 0.9333}], "translate": [{"time": 0.2667, "curve": [0.278, 0.51, 0.288, 0.51, 0.278, 5.66, 0.288, 22.13]}, {"time": 0.3, "x": 2.2, "y": 24.31, "curve": [0.404, 16.72, 0.479, 60.32, 0.404, 42.63, 0.48, 96.62]}, {"time": 0.6, "x": 64.51, "y": 102.92, "curve": [0.678, 67.22, 0.803, 40.85, 0.678, 107.02, 0.803, 67.2]}, {"time": 0.9, "x": 6.46, "y": 15.28, "curve": "stepped"}, {"time": 0.9333}]}, "bone13": {"rotate": [{"value": 0.47, "curve": "stepped"}, {"time": 0.2667, "value": 0.47, "curve": [0.382, 5.25, 0.465, 20.19]}, {"time": 0.6, "value": 20.96, "curve": [0.678, 21.4, 0.803, 17.09]}, {"time": 0.9, "value": 11.47, "curve": "stepped"}, {"time": 0.9333, "value": 0.47}], "translate": [{"x": 0.35}, {"time": 0.2667, "x": 9.32, "y": -2.36, "curve": [0.278, 4, 0.286, -14.77, 0.278, -2.23, 0.287, -1.75]}, {"time": 0.3, "x": -13.52, "y": -1.81, "curve": [0.404, -4.05, 0.479, 30.79, 0.404, -2.27, 0.479, 2.78]}, {"time": 0.6, "x": 31.21, "y": 2.39, "curve": [0.678, 31.47, 0.803, 24.76, 0.678, 2.15, 0.803, -1.61]}, {"time": 0.9, "x": 21.37, "y": 1.53, "curve": "stepped"}, {"time": 0.9333, "x": 0.35}, {"time": 1, "x": -20.96, "y": -1.36}, {"time": 1.1, "x": 0.35}]}, "bone17": {"rotate": [{"value": 8.91}, {"time": 0.2667, "value": -2.9, "curve": [0.393, -8.32, 0.486, -30.08]}, {"time": 0.6333, "value": -26.17, "curve": [0.712, -24.09, 0.836, -44.26]}, {"time": 0.9333, "value": -70.57}, {"time": 1.0667, "value": 51.12}, {"time": 1.2, "value": 8.91}], "translate": [{"x": -0.52, "y": 1.02, "curve": "stepped"}, {"time": 0.2667, "x": -0.52, "y": 1.02, "curve": [0.278, 2.93, 0.286, 14.58, 0.278, 4.26, 0.286, 15.45]}, {"time": 0.3, "x": 14.29, "y": 14.91, "curve": [0.415, 11.89, 0.499, 3.96, 0.415, 10.4, 0.499, -5.15]}, {"time": 0.6333, "x": 3.99, "y": -4.46, "curve": [0.712, 4.01, 0.836, 3.83, 0.712, -4.05, 0.836, -7.96]}, {"time": 0.9333, "x": 3.59, "y": -13.06}, {"time": 1.0667, "x": -3.69, "y": 13.93}, {"time": 1.2, "x": -0.52, "y": 1.02}]}, "bone5": {"rotate": [{"time": 0.3, "curve": [0.415, 7.08, 0.499, 28.2]}, {"time": 0.6333, "value": 30.4, "curve": [0.72, 31.82, 0.858, 18.01]}, {"time": 0.9667}], "translate": [{}, {"time": 0.2667, "x": -12.6, "y": -9.26, "curve": [0.278, -13.87, 0.289, -20.24, 0.278, -14.57, 0.286, -31.77]}, {"time": 0.3, "x": -18.05, "y": -32.06, "curve": [0.415, 4.85, 0.498, 76.19, 0.415, -34.41, 0.499, -39.5]}, {"time": 0.6333, "x": 80.23, "y": -42.13, "curve": [0.694, 82.04, 0.791, 64.43, 0.694, -43.32, 0.791, -31.72]}, {"time": 0.8667, "x": 41.46, "y": -16.59}, {"time": 0.9667, "x": -23.95, "y": -7.98}, {"time": 1.1333}]}, "bone6": {"rotate": [{"time": 0.2667, "curve": [0.278, -0.65, 0.287, -1.98]}, {"time": 0.3, "value": -2.79, "curve": [0.404, -9.16, 0.479, -28.09]}, {"time": 0.6, "value": -30.14, "curve": [0.678, -31.47, 0.803, -18.58]}, {"time": 0.9, "value": -1.77, "curve": "stepped"}, {"time": 0.9333}], "translate": [{"time": 0.2667, "curve": [0.278, -0.83, 0.289, -1.57, 0.278, 6.24, 0.288, 24.42]}, {"time": 0.3, "x": -3.56, "y": 26.78, "curve": [0.404, -21.55, 0.48, -74.55, 0.404, 46.82, 0.48, 104.98]}, {"time": 0.6, "x": -80.76, "y": 112.75, "curve": [0.678, -84.81, 0.803, -45.52, 0.678, 117.83, 0.803, 68.41]}, {"time": 0.9, "x": 5.71, "y": 3.97, "curve": "stepped"}, {"time": 0.9333}]}, "bone14": {"rotate": [{"time": 0.2667, "curve": [0.278, 4.91, 0.286, 20.97]}, {"time": 0.3, "value": 21.09, "curve": [0.519, 22.8, 0.678, 28.92]}, {"time": 0.9333, "value": 28.44}, {"time": 1.0667, "value": -8.7}, {"time": 1.1667}]}, "bone15": {"rotate": [{}, {"time": 0.2667, "value": 6.33, "curve": [0.278, -0.47, 0.285, -22.79]}, {"time": 0.3, "value": -22.82, "curve": [0.507, -23.21, 0.658, -24.6]}, {"time": 0.9, "value": -24.49, "curve": "stepped"}, {"time": 0.9333}, {"time": 1.0333, "value": 14.96}, {"time": 1.1333}]}, "bone23": {"translate": [{}, {"time": 0.2667, "x": 19.35, "y": -14.88, "curve": [0.382, 14.84, 0.465, 1.15, 0.382, -18, 0.465, -27.89]}, {"time": 0.6, "y": -28.27, "curve": [0.678, -0.67, 0.803, 5.84, 0.678, -28.5, 0.803, -26.31]}, {"time": 0.9, "x": 14.33, "y": -23.46, "curve": "stepped"}, {"time": 0.9333, "x": 2.23, "y": -19.87, "curve": [0.994, 2.2, 1.053, 0.15, 0.994, -19.58, 1.053, -1.3]}, {"time": 1.1}], "scale": [{}, {"time": 0.2667, "x": 1.109, "y": 1.109, "curve": [0.278, 1.013, 0.284, 0.698, 0.278, 1.013, 0.284, 0.698]}, {"time": 0.3, "x": 0.694, "y": 0.694, "curve": [0.404, 0.665, 0.479, 0.595, 0.404, 0.665, 0.479, 0.595]}, {"time": 0.6, "x": 0.569, "y": 0.569, "curve": [0.678, 0.552, 0.803, 0.718, 0.678, 0.552, 0.803, 0.718]}, {"time": 0.9, "x": 0.934, "y": 0.934, "curve": "stepped"}, {"time": 0.9333, "x": 1.208, "y": 1.208, "curve": [0.994, 1.205, 1.053, 1.014, 0.994, 1.205, 1.053, 1.014]}, {"time": 1.1}]}, "bone9": {"rotate": [{"time": 0.2667, "curve": [0.278, -15.08, 0.282, -67.21]}, {"time": 0.3, "value": -64.7, "curve": [0.404, -50.36, 0.479, -6.43]}, {"time": 0.6, "value": -3.16, "curve": [0.678, -1.05, 0.803, -21.52]}, {"time": 0.9, "value": -48.21, "curve": "stepped"}, {"time": 0.9333}]}, "bone20": {"rotate": [{"value": 0.26, "curve": "stepped"}, {"time": 0.2667, "value": 0.26, "curve": [0.322, 0.26, 0.378, -32.64]}, {"time": 0.4333, "value": -30.66, "curve": "stepped"}, {"time": 0.4667, "value": -1.91}, {"time": 0.6, "value": 8.18, "curve": [0.645, 8.18, 0.69, 6.93]}, {"time": 0.7333, "value": 5.39, "curve": [0.79, 3.5, 0.845, -13.22]}, {"time": 0.9, "value": -13.94, "curve": [0.911, -14.08, 0.922, 0.26]}, {"time": 0.9333, "value": 0.26}], "translate": [{"x": 0.13, "y": 0.1}, {"time": 0.2667, "x": 8.8, "y": 3.48, "curve": [0.278, 2.74, 0.286, -18.08, 0.278, 1.79, 0.286, -3.86]}, {"time": 0.3, "x": -17.22, "y": -3.75, "curve": [0.348, -14.36, 0.391, 13.96, 0.348, -3.39, 0.39, -14.46]}, {"time": 0.4333, "x": 21.08, "y": -14.61, "curve": "stepped"}, {"time": 0.4667, "x": -1.24, "y": 1.03, "curve": [0.507, 6.03, 0.55, 15.88, 0.507, 0, 0.55, 0.36]}, {"time": 0.6, "x": 15.89, "y": 0.25, "curve": [0.636, 15.9, 0.684, 16.33, 0.636, 0.16, 0.684, 0.55]}, {"time": 0.7333, "x": 12.86, "y": 1.73}, {"time": 0.9, "x": 0.41, "y": 6.08, "curve": "stepped"}, {"time": 0.9333, "x": 23.68, "y": -1.02}, {"time": 1, "x": -6.01, "y": -1.08}, {"time": 1.0667, "x": -12.15, "y": -2.26}, {"time": 1.2, "x": 0.13, "y": 0.1}], "scale": [{"time": 0.2667, "curve": [0.323, 1, 0.379, 1.018, 0.323, 1, 0.379, 1.178]}, {"time": 0.4333, "x": 1.018, "y": 1.178, "curve": "stepped"}, {"time": 0.4667, "x": 0.749, "y": 0.764, "curve": [0.512, 0.749, 0.556, 1, 0.512, 0.764, 0.556, 1]}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.9, "x": 0.743, "y": 0.743, "curve": "stepped"}, {"time": 0.9333}]}, "bone7": {"rotate": [{"time": 0.2667, "curve": [0.278, -9.62, 0.283, -41.02]}, {"time": 0.3, "value": -41.3, "curve": [0.404, -43.04, 0.479, -48.28]}, {"time": 0.6, "value": -48.77, "curve": [0.678, -49.08, 0.803, -46.03]}, {"time": 0.9, "value": -42.06, "curve": "stepped"}, {"time": 0.9333}]}, "bone21": {"translate": [{"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "x": 39.01, "y": -24.64}, {"time": 0.9, "x": 43.27, "y": -27.45, "curve": "stepped"}, {"time": 0.9333}], "scale": [{"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "x": 0.253, "y": 0.253}, {"time": 0.6, "x": 1.101, "y": 1.101, "curve": "stepped"}, {"time": 0.8333, "x": 1.101, "y": 1.101}, {"time": 0.9, "x": 0.533, "y": 0.533, "curve": "stepped"}, {"time": 0.9333}]}}}}}